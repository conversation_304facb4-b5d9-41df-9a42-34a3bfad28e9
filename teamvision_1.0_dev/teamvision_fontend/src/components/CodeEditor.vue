<template>
  <div class="code-editor-container">
    <div class="editor-toolbar" v-if="showToolbar">
      <div class="toolbar-left">
        <el-button-group class="view-mode-group">
          <el-button 
            :type="viewMode === 'formatted' ? 'primary' : ''" 
            @click="setViewMode('formatted')"
            size="mini">
            格式化
          </el-button>
          <el-button 
            :type="viewMode === 'raw' ? 'primary' : ''" 
            @click="setViewMode('raw')"
            size="mini">
            原始
          </el-button>
        </el-button-group>
        
        <el-select v-model="currentLanguage" size="mini" class="language-select">
          <el-option label="JSON" value="json"></el-option>
          <el-option label="XML" value="xml"></el-option>
          <el-option label="HTML" value="html"></el-option>
          <el-option label="JavaScript" value="javascript"></el-option>
          <el-option label="Text" value="text"></el-option>
        </el-select>
      </div>
      
      <div class="toolbar-right">
        <el-button @click="copyContent" size="mini" icon="el-icon-document-copy">
          复制
        </el-button>
        <el-button @click="downloadContent" size="mini" icon="el-icon-download">
          下载
        </el-button>
      </div>
    </div>
    
    <div class="editor-content" :style="{ height: editorHeight }">
      <div v-if="isLargeContent" class="large-content-warning">
        <i class="el-icon-warning"></i>
        <span>内容过大 ({{ formatSize(contentSize) }})，已启用分页显示</span>
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :total="totalLines"
          layout="prev, pager, next, jumper"
          @current-change="handlePageChange"
          size="mini">
        </el-pagination>
      </div>
      
      <pre 
        v-if="!isLargeContent" 
        :class="['code-content', `language-${currentLanguage}`]"
        v-html="highlightedContent">
      </pre>
      
      <pre 
        v-else
        :class="['code-content', `language-${currentLanguage}`]"
        v-html="paginatedContent">
      </pre>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CodeEditor',
  props: {
    value: {
      type: String,
      default: ''
    },
    language: {
      type: String,
      default: 'json'
    },
    readOnly: {
      type: Boolean,
      default: true
    },
    height: {
      type: String,
      default: '400px'
    },
    maxLines: {
      type: Number,
      default: 1000
    },
    showToolbar: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      viewMode: 'formatted', // formatted, raw
      currentLanguage: this.language,
      currentPage: 1,
      pageSize: 100, // 每页显示行数
      contentCache: new Map() // 缓存格式化后的内容
    }
  },
  computed: {
    editorHeight() {
      return this.showToolbar ? `calc(${this.height} - 40px)` : this.height
    },
    
    contentLines() {
      return this.value ? this.value.split('\n') : []
    },
    
    totalLines() {
      return this.contentLines.length
    },
    
    contentSize() {
      return new Blob([this.value]).size
    },
    
    isLargeContent() {
      return this.totalLines > this.maxLines || this.contentSize > 1024 * 1024 // 1MB
    },
    
    formattedContent() {
      if (!this.value) return ''
      
      const cacheKey = `${this.currentLanguage}_${this.viewMode}_${this.value.length}`
      if (this.contentCache.has(cacheKey)) {
        return this.contentCache.get(cacheKey)
      }
      
      let formatted = this.value
      
      if (this.viewMode === 'formatted') {
        try {
          if (this.currentLanguage === 'json') {
            const parsed = JSON.parse(this.value)
            formatted = JSON.stringify(parsed, null, 2)
          } else if (this.currentLanguage === 'xml') {
            formatted = this.formatXml(this.value)
          }
        } catch (error) {
          // 格式化失败，使用原始内容
          formatted = this.value
        }
      }
      
      this.contentCache.set(cacheKey, formatted)
      return formatted
    },
    
    highlightedContent() {
      if (!this.formattedContent) return ''
      
      // 简单的语法高亮（可以集成 highlight.js 或 prism.js）
      return this.applyBasicHighlight(this.formattedContent)
    },
    
    paginatedContent() {
      if (!this.isLargeContent) return this.highlightedContent
      
      const lines = this.formattedContent.split('\n')
      const startIndex = (this.currentPage - 1) * this.pageSize
      const endIndex = startIndex + this.pageSize
      const pageLines = lines.slice(startIndex, endIndex)
      
      return this.applyBasicHighlight(pageLines.join('\n'))
    }
  },
  watch: {
    language: {
      handler(newLang) {
        this.currentLanguage = newLang
      },
      immediate: true
    },
    
    value() {
      // 内容变化时清除缓存
      this.contentCache.clear()
      this.currentPage = 1
    }
  },
  methods: {
    setViewMode(mode) {
      this.viewMode = mode
      this.contentCache.clear()
    },
    
    handlePageChange(page) {
      this.currentPage = page
    },
    
    formatSize(bytes) {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },
    
    formatXml(xml) {
      // 简单的 XML 格式化
      const PADDING = ' '.repeat(2)
      const reg = /(>)(<)(\/*)/g
      let formatted = xml.replace(reg, '$1\r\n$2$3')
      let pad = 0
      
      return formatted.split('\r\n').map(line => {
        let indent = 0
        if (line.match(/.+<\/\w[^>]*>$/)) {
          indent = 0
        } else if (line.match(/^<\/\w/) && pad !== 0) {
          pad -= 1
        } else if (line.match(/^<\w[^>]*[^\/]>.*$/)) {
          indent = 1
        } else {
          indent = 0
        }
        
        const padding = PADDING.repeat(pad)
        pad += indent
        return padding + line
      }).join('\n')
    },
    
    applyBasicHighlight(content) {
      if (!content) return ''
      
      // 基础语法高亮（可以替换为更强大的高亮库）
      if (this.currentLanguage === 'json') {
        return content
          .replace(/(".*?")/g, '<span class="json-string">$1</span>')
          .replace(/(\b\d+\.?\d*\b)/g, '<span class="json-number">$1</span>')
          .replace(/\b(true|false|null)\b/g, '<span class="json-keyword">$1</span>')
      }
      
      return content
    },
    
    async copyContent() {
      try {
        await navigator.clipboard.writeText(this.formattedContent)
        this.$message.success('内容已复制到剪贴板')
      } catch (error) {
        // 降级方案
        const textArea = document.createElement('textarea')
        textArea.value = this.formattedContent
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        this.$message.success('内容已复制到剪贴板')
      }
    },
    
    downloadContent() {
      const blob = new Blob([this.formattedContent], { type: 'text/plain' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `response.${this.currentLanguage}`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
      this.$message.success('文件下载已开始')
    }
  }
}
</script>

<style scoped>
.code-editor-container {
  border: 1px solid #e0e6ed;
  border-radius: 6px;
  overflow: hidden;
}

.editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8f9fa;
  border-bottom: 1px solid #e0e6ed;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.language-select {
  width: 120px;
}

.editor-content {
  position: relative;
  overflow: auto;
}

.large-content-warning {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #fff7e6;
  border-bottom: 1px solid #ffd591;
  font-size: 12px;
  color: #d46b08;
}

.code-content {
  margin: 0;
  padding: 16px;
  font-family: 'Monaco', 'Menlo', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
  background: #fafafa;
  color: #333;
  white-space: pre-wrap;
  word-break: break-all;
}

/* JSON 语法高亮样式 */
.code-content :deep(.json-string) {
  color: #22863a;
}

.code-content :deep(.json-number) {
  color: #005cc5;
}

.code-content :deep(.json-keyword) {
  color: #d73a49;
  font-weight: bold;
}

/* 滚动条样式 */
.editor-content::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.editor-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.editor-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
  transition: background 0.2s ease;
}

.editor-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
