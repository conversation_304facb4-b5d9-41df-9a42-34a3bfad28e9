/**
 * URL 工具函数
 */

/**
 * 验证 URL 格式
 * @param {string} url - 要验证的 URL
 * @returns {Object} 验证结果 { isValid: boolean, message: string }
 */
export function validateUrl(url) {
  if (!url || typeof url !== 'string') {
    return {
      isValid: false,
      message: '请输入有效的URL'
    }
  }

  // 去除首尾空格
  url = url.trim()

  // 检查是否为空
  if (url === '') {
    return {
      isValid: false,
      message: 'URL不能为空'
    }
  }

  try {
    // 如果没有协议，自动添加 http://
    let testUrl = url
    if (!/^https?:\/\//i.test(url)) {
      testUrl = 'http://' + url
    }

    // 使用 URL 构造函数验证
    const urlObj = new URL(testUrl)

    // 检查协议是否为 http 或 https
    if (!['http:', 'https:'].includes(urlObj.protocol)) {
      return {
        isValid: false,
        message: '仅支持 HTTP 和 HTTPS 协议'
      }
    }

    // 检查主机名是否有效
    if (!urlObj.hostname || urlObj.hostname === '') {
      return {
        isValid: false,
        message: '请输入有效的主机名'
      }
    }

    return {
      isValid: true,
      message: 'URL格式正确'
    }
  } catch (error) {
    return {
      isValid: false,
      message: '请输入有效的URL格式，例如：https://example.com/api 或 example.com/api'
    }
  }
}

/**
 * 标准化 URL 格式
 * @param {string} url - 要标准化的 URL
 * @returns {string} 标准化后的 URL
 */
export function normalizeUrl(url) {
  if (!url || typeof url !== 'string') {
    return url
  }

  url = url.trim()

  // 如果没有协议，自动添加 http://
  if (url && !/^https?:\/\//i.test(url)) {
    return 'http://' + url
  }

  return url
}

/**
 * 解析 URL 参数
 * @param {string} url - URL 字符串
 * @returns {Object} 解析后的参数对象
 */
export function parseUrlParams(url) {
  try {
    const urlObj = new URL(url)
    const params = {}
    
    for (const [key, value] of urlObj.searchParams.entries()) {
      params[key] = value
    }
    
    return params
  } catch (error) {
    return {}
  }
}

/**
 * 构建带参数的 URL
 * @param {string} baseUrl - 基础 URL
 * @param {Object} params - 参数对象
 * @returns {string} 完整的 URL
 */
export function buildUrlWithParams(baseUrl, params = {}) {
  if (!baseUrl) return ''
  
  try {
    const url = new URL(baseUrl)
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== null && value !== undefined && value !== '') {
        url.searchParams.set(key, value)
      }
    })
    
    return url.toString()
  } catch (error) {
    return baseUrl
  }
}

/**
 * 检查 URL 是否为相对路径
 * @param {string} url - URL 字符串
 * @returns {boolean} 是否为相对路径
 */
export function isRelativeUrl(url) {
  if (!url) return false
  return !(/^https?:\/\//i.test(url))
}

/**
 * 获取 URL 的域名
 * @param {string} url - URL 字符串
 * @returns {string} 域名
 */
export function getDomain(url) {
  try {
    const urlObj = new URL(url)
    return urlObj.hostname
  } catch (error) {
    return ''
  }
}
