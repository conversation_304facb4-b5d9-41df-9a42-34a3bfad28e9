<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试场景 - TeamVision</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f5f5f5;
            height: 100vh;
            overflow: hidden;
        }

        .container {
            display: flex;
            height: 100vh;
            background: white;
        }

        /* 左侧导航栏 */
        .nav-sidebar {
            width: 60px;
            background: #f8f9fa;
            border-right: 1px solid #e0e0e0;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px 0;
        }

        .nav-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
            cursor: pointer;
            margin-bottom: 8px;
            color: #666;
            font-size: 18px;
            transition: all 0.2s ease;
        }

        .nav-icon:hover {
            background: #e9ecef;
            color: #333;
        }

        .nav-icon.active {
            background: #722ed1;
            color: white;
        }

        /* 左侧功能面板 */
        .left-panel {
            width: 360px;
            background: white;
            border-right: 1px solid #e0e0e0;
            display: flex;
            flex-direction: column;
        }

        .panel-tab {
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .left-header {
            padding: 16px;
            border-bottom: 1px solid #e0e0e0;
        }

        .breadcrumb {
            font-size: 12px;
            color: #666;
            margin-bottom: 12px;
        }

        .search-bar {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            margin-bottom: 12px;
        }

        .action-buttons {
            display: flex;
            gap: 8px;
            align-items: center;
            justify-content: space-between;
        }

        .new-btn {
            background: #722ed1;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .import-btn {
            background: white !important;
            color: #666 !important;
            border: 1px solid #ddd !important;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .import-btn:hover {
            background: #f5f5f5 !important;
            border-color: #722ed1 !important;
            color: #722ed1 !important;
        }

        .collection-tree {
            flex: 1;
            padding: 8px;
            overflow-y: auto;
        }

        .tree-item {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            position: relative;
        }

        .tree-item:hover {
            background: #f5f5f5;
        }

        .tree-label {
            flex: 1;
            font-size: 14px;
            color: #333;
        }

        .tree-actions {
            opacity: 0;
            transition: opacity 0.2s;
        }

        .tree-item:hover .tree-actions {
            opacity: 1;
        }

        .icon-btn {
            width: 32px;
            height: 32px;
            border: none;
            background: transparent;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
        }

        .icon-btn:hover {
            background: #f0f0f0;
        }

        .action-icon {
            width: 24px;
            height: 24px;
            border: none;
            background: transparent;
            cursor: pointer;
            color: #666;
            border-radius: 3px;
        }

        .action-icon:hover {
            background: #f0f0f0;
        }

        /* Scene相关样式 */
        .scene-section {
            margin-bottom: 16px;
        }

        .scene-header {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            background: #f8f9fa;
            border-radius: 6px;
            margin-bottom: 8px;
            cursor: pointer;
            user-select: none;
        }

        .scene-header:hover {
            background: #f0f0f0;
        }

        .scene-icon {
            margin-right: 8px;
            font-size: 16px;
            color: #722ed1;
        }

        .scene-title {
            flex: 1;
            font-size: 14px;
            font-weight: 600;
            color: #333;
        }

        .scene-count {
            font-size: 12px;
            color: #666;
            margin-right: 8px;
        }

        .scene-actions {
            display: flex;
            gap: 4px;
        }

        .scene-subsection {
            margin-left: 16px;
            padding-left: 12px;
            border-left: 2px solid #f0f0f0;
        }

        .scene-item {
            margin-left: 0;
            padding: 6px 12px;
            background: #fafafa;
            border-radius: 4px;
            margin-bottom: 4px;
        }

        .scene-item:hover {
            background: #f0f0f0;
        }

        .scene-item.selected {
            background: #e6f7ff !important;
            border-left: 3px solid #722ed1;
        }

        .scene-item-name {
            flex: 1;
            font-size: 14px;
            color: #333;
        }

        /* 右侧主工作区 */
        .right-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: white;
        }

        /* 全部场景列表样式 */
        .scenes-list-panel {
            display: flex;
            flex-direction: column;
            height: 100%;
            background: white;
        }

        .scenes-header {
            padding: 16px;
            border-bottom: 1px solid #e0e0e0;
            background: white;
        }

        .scenes-tabs {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 16px;
        }

        .scenes-tab {
            background: #722ed1;
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
        }

        .scenes-tab-add {
            width: 32px;
            height: 32px;
            border-radius: 6px;
            background: #f0f0f0;
            color: #666;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 18px;
            font-weight: bold;
        }

        .scenes-tab-add:hover {
            background: #e0e0e0;
        }

        .scenes-toolbar {
            display: flex;
            align-items: center;
            gap: 16px;
            flex-wrap: wrap;
        }

        .search-section {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .search-input {
            width: 300px;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .search-input:focus {
            outline: none;
            border-color: #722ed1;
            box-shadow: 0 0 0 2px rgba(114,46,209,0.2);
        }

        .search-btn {
            padding: 8px 12px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .search-btn:hover {
            border-color: #722ed1;
            color: #722ed1;
        }

        .filter-section {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .filter-label {
            font-size: 14px;
            color: #666;
            white-space: nowrap;
        }

        .filter-select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            background: white;
            cursor: pointer;
        }

        .filter-select:focus {
            outline: none;
            border-color: #722ed1;
        }

        .action-section {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-left: auto;
        }

        .clear-btn, .refresh-btn {
            padding: 8px 12px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .clear-btn:hover, .refresh-btn:hover {
            border-color: #722ed1;
            color: #722ed1;
        }

        .scenes-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .scenes-table-container {
            flex: 1;
            overflow: auto;
            border: 1px solid #e0e0e0;
            margin: 0 16px;
        }

        .scenes-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            font-size: 14px;
        }

        .scenes-table th {
            background: #fafafa;
            padding: 12px 8px;
            text-align: left;
            font-weight: 600;
            color: #333;
            border-bottom: 1px solid #e0e0e0;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .scenes-table td {
            padding: 12px 8px;
            border-bottom: 1px solid #f0f0f0;
            vertical-align: middle;
        }

        .scene-row:hover {
            background: #f8f9fa;
        }

        .select-all-checkbox, .row-checkbox {
            width: 16px;
            height: 16px;
            cursor: pointer;
        }

        .sort-icon {
            margin-left: 4px;
            font-size: 12px;
            opacity: 0.6;
            cursor: pointer;
        }

        .priority-badge {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .priority-badge.p0 {
            background: #fff2e8;
            color: #d46b08;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-badge.progress {
            background: #e6f7ff;
            color: #1890ff;
        }

        .result-badge {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .result-badge.success {
            background: #f6ffed;
            color: #52c41a;
        }

        .tag-badge {
            background: #f0f0f0;
            color: #666;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
        }

        .actions-cell {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .action-btn {
            padding: 4px 8px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            color: #666;
            transition: all 0.2s;
        }

        .action-btn:hover {
            border-color: #722ed1;
            color: #722ed1;
        }

        .edit-btn:hover {
            border-color: #1890ff;
            color: #1890ff;
        }

        .run-btn:hover {
            border-color: #52c41a;
            color: #52c41a;
        }

        .copy-btn:hover {
            border-color: #722ed1;
            color: #722ed1;
        }

        .pagination-container {
            padding: 16px;
            border-top: 1px solid #e0e0e0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .pagination-info {
            font-size: 14px;
            color: #666;
        }

        .pagination-controls {
            display: flex;
            gap: 8px;
        }

        .pagination-btn {
            width: 32px;
            height: 32px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
        }

        .pagination-btn:disabled {
            background: #722ed1;
            color: white;
            border-color: #722ed1;
            cursor: not-allowed;
        }

        .pagination-btn:not(:disabled):hover {
            border-color: #722ed1;
            color: #722ed1;
        }

        /* 新建场景面板样式 */
        .scene-form-panel {
            display: flex;
            flex-direction: column;
            height: 100%;
            background: white;
        }

        .scene-form-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            border-bottom: 1px solid #e0e0e0;
            background: white;
            height: 48px;
        }

        .scene-form-tabs {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .scene-form-tab {
            display: flex;
            align-items: center;
            padding: 8px 16px;
            border-radius: 6px 6px 0 0;
            cursor: pointer;
            background: #f5f5f5;
            border: 1px solid transparent;
            border-bottom: none;
            position: relative;
            min-width: 120px;
            gap: 8px;
        }

        .scene-form-tab.active {
            background: white;
            border-color: #e0e0e0;
            color: #722ed1;
            font-weight: 600;
        }

        .scene-form-tab.add-tab {
            width: 32px;
            height: 32px;
            min-width: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f0f0f0;
            color: #666;
            font-size: 18px;
            font-weight: bold;
        }

        .scene-form-actions {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .btn-icon {
            width: 32px;
            height: 32px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
        }

        .btn-primary {
            background: #722ed1;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
        }

        .btn-secondary {
            background: white;
            color: #666;
            border: 1px solid #ddd;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
        }

        .scene-form-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .scene-form-tabs-content {
            display: flex;
            border-bottom: 1px solid #e0e0e0;
            background: white;
            padding: 0 16px;
        }

        .scene-form-tab-item {
            padding: 12px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            font-size: 14px;
            color: #666;
        }

        .scene-form-tab-item.active {
            color: #722ed1;
            border-bottom-color: #722ed1;
        }

        .scene-form-body {
            flex: 1;
            display: flex;
            background: #fafafa;
        }

        .scene-steps-content {
            flex: 1;
            padding: 20px;
            background: white;
            margin: 16px 0 16px 16px;
            border-radius: 6px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .steps-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
            font-size: 14px;
            color: #666;
        }

        .add-step-btn {
            background: #722ed1;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .steps-empty {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 200px;
            color: #999;
            font-size: 14px;
        }

        .scene-form-sidebar {
            width: 300px;
            background: white;
            border-left: 1px solid #e0e0e0;
            padding: 20px;
            overflow-y: auto;
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .form-label.required::after {
            content: '*';
            color: #ff4d4f;
            margin-left: 4px;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-input:focus {
            outline: none;
            border-color: #722ed1;
            box-shadow: 0 0 0 2px rgba(114,46,209,0.2);
        }

        .form-textarea {
            width: 100%;
            min-height: 80px;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            resize: vertical;
            font-family: inherit;
        }

        .form-textarea:focus {
            outline: none;
            border-color: #722ed1;
            box-shadow: 0 0 0 2px rgba(114,46,209,0.2);
        }

        .radio-group {
            display: flex;
            gap: 16px;
        }

        .radio-item {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            font-size: 14px;
        }

        .radio-item input[type="radio"] {
            display: none;
        }

        .radio-mark {
            width: 16px;
            height: 16px;
            border: 2px solid #d9d9d9;
            border-radius: 50%;
            position: relative;
            transition: all 0.2s;
        }

        .radio-item input[type="radio"]:checked + .radio-mark {
            border-color: #722ed1;
        }

        .radio-item input[type="radio"]:checked + .radio-mark::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 8px;
            height: 8px;
            background: #722ed1;
            border-radius: 50%;
            transform: translate(-50%, -50%);
        }

        .tab-close {
            margin-left: 8px;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #666;
            opacity: 0;
            transition: opacity 0.2s;
        }

        .scene-form-tab:hover .tab-close {
            opacity: 1;
        }

        .tab-close:hover {
            background: #ff4d4f;
            color: white;
        }

        /* 步骤列表样式 */
        .steps-container {
            flex: 1;
            overflow-y: auto;
        }

        .step-item {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            margin-bottom: 8px;
            background: #fafafa;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .step-item:hover {
            background: #f0f0f0;
            border-color: #722ed1;
        }

        .step-item.selected {
            background: #e6f7ff;
            border-color: #722ed1;
        }

        .step-checkbox {
            margin-right: 12px;
            cursor: pointer;
        }

        .step-toggle {
            width: 40px;
            height: 20px;
            background: #52c41a;
            border-radius: 10px;
            position: relative;
            margin-right: 12px;
            cursor: pointer;
        }

        .step-toggle::after {
            content: '';
            position: absolute;
            width: 16px;
            height: 16px;
            background: white;
            border-radius: 50%;
            top: 2px;
            right: 2px;
            transition: all 0.2s;
        }

        .step-toggle.disabled {
            background: #d9d9d9;
        }

        .step-toggle.disabled::after {
            left: 2px;
        }

        .step-run-btn {
            width: 24px;
            height: 24px;
            background: #722ed1;
            color: white;
            border: none;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            cursor: pointer;
            font-size: 12px;
        }

        .step-content {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .step-type-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            white-space: nowrap;
        }

        .step-type-badge.controller {
            background: #fff7e6;
            color: #d48806;
        }

        .step-type-badge.wait {
            background: #f6ffed;
            color: #389e0d;
        }

        .step-type-badge.api {
            background: #e6f7ff;
            color: #1890ff;
        }

        .step-type-badge.case {
            background: #f9f0ff;
            color: #722ed1;
        }

        .step-method-badge {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: 600;
            color: white;
            margin-right: 8px;
        }

        .step-method-badge.get {
            background: #52c41a;
        }

        .step-method-badge.put {
            background: #faad14;
        }

        .step-method-badge.delete {
            background: #ff4d4f;
        }

        .step-description {
            flex: 1;
            font-size: 14px;
            color: #333;
        }

        .step-operator {
            font-size: 12px;
            color: #666;
            margin: 0 8px;
        }

        .step-value {
            font-size: 12px;
            color: #333;
            background: #f5f5f5;
            padding: 2px 6px;
            border-radius: 3px;
        }

        .step-actions {
            display: flex;
            gap: 4px;
            opacity: 0;
            transition: opacity 0.2s;
        }

        .step-item:hover .step-actions {
            opacity: 1;
        }

        .step-action-btn {
            width: 24px;
            height: 24px;
            border: none;
            background: transparent;
            border-radius: 3px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 14px;
        }

        .step-action-btn:hover {
            background: #f0f0f0;
        }

        .basic-info-section {
            margin-bottom: 20px;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 6px;
        }

        .basic-info-row {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .basic-info-row:last-child {
            margin-bottom: 0;
        }

        .basic-info-label {
            color: #666;
            margin-right: 8px;
            min-width: 60px;
        }

        .basic-info-value {
            color: #333;
        }

        .priority-indicator {
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .priority-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #d46b08;
        }

        /* 添加步骤按钮样式调整 */
        .add-step-in-list {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px 16px;
            margin-bottom: 8px;
            background: #f8f9fa;
            border: 2px dashed #d9d9d9;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s;
            color: #666;
            font-size: 14px;
            gap: 8px;
        }

        .add-step-in-list:hover {
            background: #f0f0f0;
            border-color: #722ed1;
            color: #722ed1;
        }

        .add-step-in-list .plus-icon {
            font-size: 16px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 左侧导航栏 -->
        <div class="nav-sidebar">
            <div class="nav-icon active" title="Test Scenes">
                <span>场景</span>
            </div>
        </div>

        <!-- 左侧功能面板 -->
        <div class="left-panel">
            <!-- Test Scene 选项卡 -->
            <div id="scenes-tab" class="panel-tab">
                <div class="left-header">
                    <div class="breadcrumb">
                        Personal Workspace > Test Scene
                    </div>
                    <input type="text" class="search-bar" placeholder="请输入场景名称进行搜索" oninput="searchScenes(this.value)">
                    <div class="action-buttons">
                        <button class="new-btn" onclick="showNewSceneDirDialog()">
                            <span>+</span> 新建场景集
                        </button>
                        <button class="import-btn" onclick="importScene()">
                            导入场景
                        </button>
                    </div>
                </div>
                
                <div class="collection-tree">
                    <!-- 全部场景标题 -->
                    <div class="scene-section">
                        <div class="scene-header" onclick="toggleSceneSection(this)">
                            <span class="scene-icon">📁</span>
                            <span class="scene-title">全部场景</span>
                            <span class="scene-count">(2)</span>
                            <div class="scene-actions">
                                <button class="icon-btn collapse-btn" title="展开/收起">
                                    <span>−</span>
                                </button>
                                <button class="icon-btn" title="更多操作" onclick="event.stopPropagation(); showSceneMenu(this)">
                                    <span>⋯</span>
                                </button>
                            </div>
                        </div>
                        
                        <!-- 未规划场景 -->
                        <div class="scene-subsection">
                            <!-- demo场景条目 -->
                            <div class="tree-item scene-item" onclick="selectScene(this, 'demo')">
                                <span class="scene-item-name">demo</span>
                                <div class="tree-actions">
                                    <button class="action-icon" title="编辑" onclick="event.stopPropagation(); editScene('demo')">✏</button>
                                    <button class="action-icon" title="删除" onclick="event.stopPropagation(); deleteScene('demo')">🗑</button>
                                    <button class="action-icon" title="更多" onclick="event.stopPropagation(); showSceneItemMenu(this, 'demo')">⋯</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧主工作区 -->
        <div class="right-panel">
            <!-- 全部场景面板 -->
            <div id="scenes-list-panel" class="scenes-list-panel">
                <div class="scenes-header">
                    <div class="scenes-tabs">
                        <div class="scenes-tab active">
                            <span>全部场景</span>
                        </div>
                        <div class="scenes-tab-add" onclick="showAddSceneMenu()">
                            <span>+</span>
                        </div>
                    </div>
                    
                    <div class="scenes-toolbar">
                        <div class="search-section">
                            <input type="text" class="search-input" placeholder="请过ID/名称/负责人等信息" oninput="filterScenes(this.value)">
                            <button class="search-btn">🔍</button>
                        </div>
                        
                        <div class="filter-section">
                            <span class="filter-label">场景等级:</span>
                            <select class="filter-select" onchange="filterByLevel(this.value)">
                                <option value="">全部数据</option>
                                <option value="P0">P0</option>
                                <option value="P1">P1</option>
                                <option value="P2">P2</option>
                            </select>
                        </div>
                        
                        <div class="action-section">
                            <button class="clear-btn" onclick="clearFilters()">🗑 清除</button>
                            <button class="refresh-btn" onclick="refreshScenes()">🔄</button>
                        </div>
                    </div>
                </div>
                
                <div class="scenes-content">
                    <div class="scenes-table-container">
                        <table class="scenes-table">
                            <thead>
                                <tr>
                                    <th style="width: 50px;">
                                        <input type="checkbox" class="select-all-checkbox" onchange="toggleSelectAll(this)">
                                    </th>
                                    <th style="width: 80px;">ID <span class="sort-icon">📶</span></th>
                                    <th>场景名称 <span class="sort-icon">📶</span></th>
                                    <th style="width: 120px;">场景等级 <span class="sort-icon">📶</span></th>
                                    <th style="width: 100px;">状态 <span class="sort-icon">📶</span></th>
                                    <th style="width: 150px;">执行结果 <span class="sort-icon">📶</span></th>
                                    <th style="width: 120px;">标签</th>
                                    <th style="width: 120px;">场景环境</th>
                                    <th style="width: 200px;">操作 <span class="sort-icon">📶</span></th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="scene-row">
                                    <td><input type="checkbox" class="row-checkbox"></td>
                                    <td>2</td>
                                    <td>发布并删除文章</td>
                                    <td><span class="priority-badge p0">⭕ P0</span></td>
                                    <td><span class="status-badge progress">进行中</span></td>
                                    <td><span class="result-badge success">🟢 成功</span></td>
                                    <td><span class="tag-badge">文章发布至流程</span></td>
                                    <td>Halo</td>
                                    <td class="actions-cell">
                                        <button class="action-btn edit-btn" onclick="editSceneDetails(2)">编辑</button>
                                        <button class="action-btn run-btn" onclick="runScene(2)">执行</button>
                                        <button class="action-btn copy-btn" onclick="copyScene(2)">复制</button>
                                        <button class="action-btn more-btn" onclick="showSceneActions(2)">⋯</button>
                                    </td>
                                </tr>
                                <tr class="scene-row">
                                    <td><input type="checkbox" class="row-checkbox"></td>
                                    <td>1</td>
                                    <td>发布文章流程</td>
                                    <td><span class="priority-badge p0">⭕ P0</span></td>
                                    <td><span class="status-badge progress">进行中</span></td>
                                    <td><span class="result-badge success">🟢 成功</span></td>
                                    <td>-</td>
                                    <td>Halo</td>
                                    <td class="actions-cell">
                                        <button class="action-btn edit-btn" onclick="editSceneDetails(1)">编辑</button>
                                        <button class="action-btn run-btn" onclick="runScene(1)">执行</button>
                                        <button class="action-btn copy-btn" onclick="copyScene(1)">复制</button>
                                        <button class="action-btn more-btn" onclick="showSceneActions(1)">⋯</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="pagination-container">
                        <div class="pagination-info">
                            共 2 条
                        </div>
                        <div class="pagination-controls">
                            <button class="pagination-btn prev-btn" disabled>
                                <span>1</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 新建场景面板 -->
            <div id="new-scene-panel" class="scene-form-panel" style="display: none;">
                <div class="scene-form-header">
                    <div class="scene-form-tabs">
                        <div class="scene-form-tab active">
                            <span>全部场景</span>
                        </div>
                        <div class="scene-form-tab">
                            <span>新建场景</span>
                            <button class="tab-close" onclick="hideNewScenePanel()">×</button>
                        </div>
                        <div class="scene-form-tab add-tab" onclick="showAddSceneMenu()">
                            <span>+</span>
                        </div>
                    </div>
                    <div class="scene-form-actions">
                        <button class="btn-icon" title="Halo">
                            <span>👁</span>
                        </button>
                        <button class="btn-primary" onclick="saveNewScene()">
                            <span>服务端执行</span>
                        </button>
                        <button class="btn-secondary" onclick="saveNewScene()">
                            <span>保存</span>
                        </button>
                    </div>
                </div>

                <div class="scene-form-content">
                    <div class="scene-form-tabs-content">
                        <div class="scene-form-tab-item active" onclick="switchSceneFormTab('steps')">步骤</div>
                        <div class="scene-form-tab-item" onclick="switchSceneFormTab('params')">参数</div>
                        <div class="scene-form-tab-item" onclick="switchSceneFormTab('headers')">前/后置</div>
                        <div class="scene-form-tab-item" onclick="switchSceneFormTab('assertions')">断言</div>
                        <div class="scene-form-tab-item" onclick="switchSceneFormTab('settings')">设置</div>
                    </div>

                    <div class="scene-form-body">
                        <div class="scene-steps-content">
                            <div class="steps-header">
                                <span>共 0 个步骤</span>
                                <div style="display: flex; gap: 8px;">
                                    <button class="icon-btn" title="刷新">🔄</button>
                                </div>
                            </div>
                            <div class="steps-container">
                                <div class="steps-empty" style="display: none;">
                                    <div class="empty-message">暂无步骤，请点击上方按钮添加</div>
                                </div>
                                <!-- 添加步骤按钮移到底部 -->
                                <div class="add-step-in-list" onclick="addSceneStep()">
                                    <span class="plus-icon">+</span>
                                    <span>添加步骤</span>
                                </div>
                            </div>
                        </div>

                        <div class="scene-form-sidebar">
                            <div class="form-group">
                                <label class="form-label required">场景名称</label>
                                <input type="text" class="form-input" placeholder="请输入场景名称" id="sceneName">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">所属模块</label>
                                <select class="form-input" id="sceneModule">
                                    <option value="">未规划场景</option>
                                    <option value="login">登录模块</option>
                                    <option value="user">用户模块</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">场景等级</label>
                                <div class="radio-group">
                                    <label class="radio-item">
                                        <input type="radio" name="sceneLevel" value="P0" checked>
                                        <span class="radio-mark"></span>
                                        <span>P0</span>
                                    </label>
                                    <label class="radio-item">
                                        <input type="radio" name="sceneLevel" value="P1">
                                        <span class="radio-mark"></span>
                                        <span>P1</span>
                                    </label>
                                    <label class="radio-item">
                                        <input type="radio" name="sceneLevel" value="P2">
                                        <span class="radio-mark"></span>
                                        <span>P2</span>
                                    </label>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">场景状态</label>
                                <select class="form-input" id="sceneStatus">
                                    <option value="progress">进行中</option>
                                    <option value="completed">已完成</option>
                                    <option value="pending">待处理</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">标签</label>
                                <input type="text" class="form-input" placeholder="添加标签，回车键确定" id="sceneTags">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">描述</label>
                                <textarea class="form-textarea" placeholder="请对场景进行描述" id="sceneDescription"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 编辑场景面板 -->
            <div id="edit-scene-panel" class="scene-form-panel" style="display: none;">
                <div class="scene-form-header">
                    <div class="scene-form-tabs">
                        <div class="scene-form-tab">
                            <span>全部场景</span>
                        </div>
                        <div class="scene-form-tab active">
                            <span id="edit-scene-tab-title">发布并删除文章</span>
                            <button class="tab-close" onclick="hideEditScenePanel()">×</button>
                        </div>
                        <div class="scene-form-tab add-tab" onclick="showAddSceneMenu()">
                            <span>+</span>
                        </div>
                    </div>
                    <div class="scene-form-actions">
                        <button class="btn-icon" title="Halo">
                            <span>👁</span>
                        </button>
                        <button class="btn-primary" onclick="runEditedScene()">
                            <span>服务端执行</span>
                        </button>
                        <button class="btn-secondary" onclick="saveEditedScene()">
                            <span>保存</span>
                        </button>
                    </div>
                </div>

                <div class="scene-form-content">
                    <!-- 场景基本信息 -->
                    <div class="basic-info-section">
                        <div class="basic-info-row">
                            <span class="basic-info-label">进行中</span>
                            <span class="priority-indicator">
                                <span class="priority-dot"></span>
                                <span>P0</span>
                            </span>
                            <span class="basic-info-value">[2] 发布并删除文章</span>
                            <span style="margin-left: 8px;">⭐</span>
                            <span style="margin-left: 8px;">📋</span>
                        </div>
                        <div class="basic-info-row">
                            <span class="basic-info-label">标签</span>
                            <span class="basic-info-value">文章发布至流程</span>
                        </div>
                        <div class="basic-info-row">
                            <span class="basic-info-label">描述</span>
                            <span class="basic-info-value">这个示例展示了场景的全部功能</span>
                        </div>
                    </div>

                    <div class="scene-form-tabs-content">
                        <div class="scene-form-tab-item active" onclick="switchEditFormTab('steps')">步骤</div>
                        <div class="scene-form-tab-item" onclick="switchEditFormTab('params')">参数</div>
                        <div class="scene-form-tab-item" onclick="switchEditFormTab('headers')">前/后置</div>
                        <div class="scene-form-tab-item" onclick="switchEditFormTab('assertions')">断言</div>
                        <div class="scene-form-tab-item" onclick="switchEditFormTab('execution')">执行历史</div>
                        <div class="scene-form-tab-item" onclick="switchEditFormTab('changes')">变更历史</div>
                        <div class="scene-form-tab-item" onclick="switchEditFormTab('settings')">设置</div>
                    </div>

                    <div class="scene-form-body">
                        <div class="scene-steps-content">
                            <div class="steps-header">
                                <span>共 9 个步骤</span>
                                <div style="display: flex; gap: 8px;">
                                    <button class="icon-btn" title="刷新">🔄</button>
                                </div>
                            </div>
                            
                            <div class="steps-container" id="edit-steps-container">
                                <!-- 步骤1: 条件控制器 -->
                                <div class="step-item">
                                    <input type="checkbox" class="step-checkbox" checked>
                                    <span class="step-run-btn">1</span>
                                    <div class="step-toggle"></div>
                                    <div class="step-content">
                                        <span class="step-type-badge controller">条件控制器</span>
                                        <span class="step-description">${count}</span>
                                        <span class="step-operator">大于</span>
                                        <span class="step-value">1</span>
                                        <span class="step-description" style="margin-left: 16px;">条件控制器</span>
                                    </div>
                                    <div class="step-actions">
                                        <button class="step-action-btn" title="编辑">✏</button>
                                        <button class="step-action-btn" title="删除">🗑</button>
                                        <button class="step-action-btn" title="更多">⋯</button>
                                    </div>
                                </div>

                                <!-- 步骤2: 等待时间 -->
                                <div class="step-item">
                                    <input type="checkbox" class="step-checkbox" checked>
                                    <span class="step-run-btn">2</span>
                                    <div class="step-toggle"></div>
                                    <div class="step-content">
                                        <span class="step-type-badge wait">等待时间</span>
                                        <span class="step-description">等待(ms): 6000</span>
                                        <span style="margin-left: auto; color: #666;">等待60S确保文章创建成功</span>
                                    </div>
                                    <div class="step-actions">
                                        <button class="step-action-btn" title="编辑">✏</button>
                                        <button class="step-action-btn" title="删除">🗑</button>
                                        <button class="step-action-btn" title="更多">⋯</button>
                                    </div>
                                </div>

                                <!-- 步骤3: 基础API -->
                                <div class="step-item">
                                    <input type="checkbox" class="step-checkbox" checked>
                                    <span class="step-run-btn">3</span>
                                    <div class="step-toggle"></div>
                                    <div class="step-content">
                                        <span class="step-type-badge api">基础 API</span>
                                        <span class="step-method-badge get">GET</span>
                                        <span class="step-description">获取文章查正文内容，确保创建的文章一致性</span>
                                    </div>
                                    <div class="step-actions">
                                        <button class="step-action-btn" title="编辑">✏</button>
                                        <button class="step-action-btn" title="删除">🗑</button>
                                        <button class="step-action-btn" title="更多">⋯</button>
                                    </div>
                                </div>

                                <!-- 步骤4: 引用API -->
                                <div class="step-item">
                                    <input type="checkbox" class="step-checkbox" checked>
                                    <span class="step-run-btn">4</span>
                                    <div class="step-toggle"></div>
                                    <div class="step-content">
                                        <span class="step-type-badge api">引用 API</span>
                                        <span class="step-method-badge put">PUT</span>
                                        <span class="step-description">将文章移入回收站，确保回收流程正常</span>
                                    </div>
                                    <div class="step-actions">
                                        <button class="step-action-btn" title="编辑">✏</button>
                                        <button class="step-action-btn" title="删除">🗑</button>
                                        <button class="step-action-btn" title="更多">⋯</button>
                                    </div>
                                </div>

                                <!-- 步骤5: 引用API -->
                                <div class="step-item">
                                    <input type="checkbox" class="step-checkbox" checked>
                                    <span class="step-run-btn">5</span>
                                    <div class="step-toggle"></div>
                                    <div class="step-content">
                                        <span class="step-type-badge api">引用 API</span>
                                        <span class="step-method-badge delete">DELETE</span>
                                        <span class="step-description">删除文章，清理测试数据</span>
                                    </div>
                                    <div class="step-actions">
                                        <button class="step-action-btn" title="编辑">✏</button>
                                        <button class="step-action-btn" title="删除">🗑</button>
                                        <button class="step-action-btn" title="更多">⋯</button>
                                    </div>
                                </div>

                                <!-- 步骤6: 等待时间 -->
                                <div class="step-item">
                                    <input type="checkbox" class="step-checkbox" checked>
                                    <span class="step-run-btn">6</span>
                                    <div class="step-toggle"></div>
                                    <div class="step-content">
                                        <span class="step-type-badge wait">等待时间</span>
                                        <span class="step-description">等待(ms): 3000</span>
                                        <span style="margin-left: auto; color: #666;">等待时间</span>
                                    </div>
                                    <div class="step-actions">
                                        <button class="step-action-btn" title="编辑">✏</button>
                                        <button class="step-action-btn" title="删除">🗑</button>
                                        <button class="step-action-btn" title="更多">⋯</button>
                                    </div>
                                </div>

                                <!-- 步骤7: 基础CASE -->
                                <div class="step-item">
                                    <input type="checkbox" class="step-checkbox" checked>
                                    <span class="step-run-btn">7</span>
                                    <div class="step-toggle"></div>
                                    <div class="step-content">
                                        <span class="step-type-badge case">基础 CASE</span>
                                        <span class="step-method-badge get">GET</span>
                                        <span class="step-description">查询被删除的文章是否还存在</span>
                                    </div>
                                    <div class="step-actions">
                                        <button class="step-action-btn" title="编辑">✏</button>
                                        <button class="step-action-btn" title="删除">🗑</button>
                                        <button class="step-action-btn" title="更多">⋯</button>
                                    </div>
                                </div>

                                <!-- 步骤8: 条件控制器 -->
                                <div class="step-item">
                                    <input type="checkbox" class="step-checkbox" checked>
                                    <span class="step-run-btn">8</span>
                                    <div class="step-toggle"></div>
                                    <div class="step-content">
                                        <span class="step-type-badge controller">条件控制器</span>
                                        <span class="step-description">${title_404}</span>
                                        <span class="step-operator">不等于</span>
                                        <span class="step-value">Not Found</span>
                                        <span class="step-description" style="margin-left: 16px;">判断被删除的文章是否还存在</span>
                                    </div>
                                    <div class="step-actions">
                                        <button class="step-action-btn" title="编辑">✏</button>
                                        <button class="step-action-btn" title="删除">🗑</button>
                                        <button class="step-action-btn" title="更多">⋯</button>
                                    </div>
                                </div>

                                <!-- 步骤9: 循环控制器 -->
                                <div class="step-item">
                                    <input type="checkbox" class="step-checkbox" checked>
                                    <span class="step-run-btn">1</span>
                                    <div class="step-toggle"></div>
                                    <div class="step-content">
                                        <span class="step-type-badge controller">循环控制器</span>
                                        <span class="step-description">while</span>
                                        <span class="step-operator">条件</span>
                                        <span class="step-value">${count}</span>
                                        <span class="step-operator">等于</span>
                                        <span class="step-value">2</span>
                                        <span class="step-description" style="margin-left: 16px;">超时(ms): 3000</span>
                                        <span style="margin-left: auto; color: #666;">循环控制器</span>
                                    </div>
                                    <div class="step-actions">
                                        <button class="step-action-btn" title="编辑">✏</button>
                                        <button class="step-action-btn" title="删除">🗑</button>
                                        <button class="step-action-btn" title="更多">⋯</button>
                                    </div>
                                </div>

                                <!-- 添加步骤按钮移到底部 -->
                                <div class="add-step-in-list" onclick="addEditSceneStep()">
                                    <span class="plus-icon">+</span>
                                    <span>添加步骤</span>
                                </div>
                            </div>
                        </div>

                        <div class="scene-form-sidebar">
                            <div class="form-group">
                                <label class="form-label required">场景名称</label>
                                <input type="text" class="form-input" placeholder="请输入场景名称" id="editSceneName">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">所属模块</label>
                                <select class="form-input" id="editSceneModule">
                                    <option value="">未规划场景</option>
                                    <option value="login">登录模块</option>
                                    <option value="user">用户模块</option>
                                    <option value="article">文章模块</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">场景等级</label>
                                <div class="radio-group">
                                    <label class="radio-item">
                                        <input type="radio" name="editSceneLevel" value="P0">
                                        <span class="radio-mark"></span>
                                        <span>P0</span>
                                    </label>
                                    <label class="radio-item">
                                        <input type="radio" name="editSceneLevel" value="P1">
                                        <span class="radio-mark"></span>
                                        <span>P1</span>
                                    </label>
                                    <label class="radio-item">
                                        <input type="radio" name="editSceneLevel" value="P2">
                                        <span class="radio-mark"></span>
                                        <span>P2</span>
                                    </label>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">场景状态</label>
                                <select class="form-input" id="editSceneStatus">
                                    <option value="progress">进行中</option>
                                    <option value="completed">已完成</option>
                                    <option value="pending">待处理</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">标签</label>
                                <input type="text" class="form-input" placeholder="添加标签，回车键确定" id="editSceneTags">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">描述</label>
                                <textarea class="form-textarea" placeholder="请对场景进行描述" id="editSceneDescription"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 显示新建场景对话框 - 修改为显示右侧面板
        function showNewSceneDialog() {
            document.getElementById('scenes-list-panel').style.display = 'none';
            document.getElementById('new-scene-panel').style.display = 'flex';
        }

        // 显示新建场景集对话框
        function showNewSceneDirDialog() {
            alert('新建场景集');
        }

        // 显示添加场景菜单
        function showAddSceneMenu() {
            document.getElementById('scenes-list-panel').style.display = 'none';
            document.getElementById('new-scene-panel').style.display = 'flex';
        }

        // 隐藏新建场景面板
        function hideNewScenePanel() {
            document.getElementById('new-scene-panel').style.display = 'none';
            document.getElementById('scenes-list-panel').style.display = 'flex';
        }

        // 存储当前编辑的场景数据
        let currentEditingScene = null;

        // 场景数据模拟
        const scenesData = {
            1: {
                id: 1,
                name: '发布文章流程',
                module: 'article',
                level: 'P0',
                status: 'progress',
                tags: '文章发布流程',
                description: '完整的文章发布流程测试场景',
                steps: []
            },
            2: {
                id: 2,
                name: '发布并删除文章',
                module: 'article',
                level: 'P0',
                status: 'progress',
                tags: '文章发布至流程',
                description: '这个示例展示了场景的全部功能',
                steps: [
                    { type: 'controller', description: '条件控制器' },
                    { type: 'wait', description: '等待60S确保文章创建成功' },
                    { type: 'api', method: 'GET', description: '获取文章查正文内容，确保创建的文章一致性' },
                    { type: 'api', method: 'PUT', description: '将文章移入回收站，确保回收流程正常' },
                    { type: 'api', method: 'DELETE', description: '删除文章，清理测试数据' },
                    { type: 'wait', description: '等待时间' },
                    { type: 'case', method: 'GET', description: '查询被删除的文章是否还存在' },
                    { type: 'controller', description: '判断被删除的文章是否还存在' },
                    { type: 'controller', description: '循环控制器' }
                ]
            }
        };

        // 显示编辑场景面板
        function showEditScenePanel(sceneId) {
            currentEditingScene = scenesData[sceneId];
            if (!currentEditingScene) {
                alert('场景数据不存在');
                return;
            }

            // 隐藏其他面板
            document.getElementById('scenes-list-panel').style.display = 'none';
            document.getElementById('new-scene-panel').style.display = 'none';
            
            // 显示编辑面板
            document.getElementById('edit-scene-panel').style.display = 'flex';
            
            // 填充表单数据
            populateEditForm(currentEditingScene);
            
            // 更新标题
            document.getElementById('edit-scene-tab-title').textContent = `编辑: ${currentEditingScene.name}`;
        }

        // 填充编辑表单
        function populateEditForm(sceneData) {
            document.getElementById('editSceneName').value = sceneData.name || '';
            document.getElementById('editSceneModule').value = sceneData.module || '';
            document.getElementById('editSceneStatus').value = sceneData.status || 'progress';
            document.getElementById('editSceneTags').value = sceneData.tags || '';
            document.getElementById('editSceneDescription').value = sceneData.description || '';
            
            // 设置场景等级
            const levelRadio = document.querySelector(`input[name="editSceneLevel"][value="${sceneData.level}"]`);
            if (levelRadio) {
                levelRadio.checked = true;
            }
            
            // 更新步骤数量显示
            const stepCount = sceneData.steps ? sceneData.steps.length : 0;
            document.querySelector('#edit-scene-panel .steps-header span').textContent = `共 ${stepCount} 个步骤`;
        }

        // 隐藏编辑场景面板
        function hideEditScenePanel() {
            document.getElementById('edit-scene-panel').style.display = 'none';
            document.getElementById('scenes-list-panel').style.display = 'flex';
            currentEditingScene = null;
        }

        // 切换编辑表单选项卡
        function switchEditFormTab(tabName) {
            document.querySelectorAll('#edit-scene-panel .scene-form-tab-item').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');
            
            console.log('切换编辑表单到选项卡:', tabName);
        }

        // 添加编辑场景步骤
        function addEditSceneStep() {
            console.log('为编辑场景添加步骤');
        }

        // 运行编辑的场景
        function runEditedScene() {
            if (!currentEditingScene) return;
            console.log('运行编辑的场景:', currentEditingScene.id);
        }

        // 保存编辑的场景
        function saveEditedScene() {
            if (!currentEditingScene) return;

            const sceneName = document.getElementById('editSceneName').value;
            const sceneModule = document.getElementById('editSceneModule').value;
            const sceneLevel = document.querySelector('input[name="editSceneLevel"]:checked')?.value;
            const sceneStatus = document.getElementById('editSceneStatus').value;
            const sceneTags = document.getElementById('editSceneTags').value;
            const sceneDescription = document.getElementById('editSceneDescription').value;

            if (!sceneName.trim()) {
                alert('请输入场景名称');
                return;
            }

            // 更新场景数据
            const updatedData = {
                ...currentEditingScene,
                name: sceneName,
                module: sceneModule,
                level: sceneLevel,
                status: sceneStatus,
                tags: sceneTags,
                description: sceneDescription
            };

            // 保存到数据存储
            scenesData[currentEditingScene.id] = updatedData;

            // 更新表格中的显示
            updateSceneInTable(updatedData);

            console.log('保存编辑场景:', updatedData);
            alert('场景保存成功！');
            hideEditScenePanel();
        }

        // 更新表格中的场景显示
        function updateSceneInTable(sceneData) {
            const rows = document.querySelectorAll('.scene-row');
            rows.forEach(row => {
                const idCell = row.cells[1];
                if (idCell && idCell.textContent == sceneData.id) {
                    // 更新场景名称
                    row.cells[2].textContent = sceneData.name;
                    
                    // 更新优先级
                    const priorityCell = row.cells[3];
                    priorityCell.innerHTML = `<span class="priority-badge p0">⭕ ${sceneData.level}</span>`;
                    
                    // 更新状态
                    const statusCell = row.cells[4];
                    const statusMap = {
                        'progress': '进行中',
                        'completed': '已完成',
                        'pending': '待处理'
                    };
                    statusCell.innerHTML = `<span class="status-badge progress">${statusMap[sceneData.status] || sceneData.status}</span>`;
                    
                    // 更新标签
                    const tagCell = row.cells[6];
                    tagCell.innerHTML = sceneData.tags ? `<span class="tag-badge">${sceneData.tags}</span>` : '-';
                }
            });
        }

        // 显示场景菜单
        function showSceneMenu(button) {
            console.log('显示场景分组菜单');
        }

        // 显示场景项菜单
        function showSceneItemMenu(button, sceneName) {
            console.log('显示场景项菜单:', sceneName);
        }

        // 导入场景
        function importScene() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        try {
                            const data = JSON.parse(e.target.result);
                            console.log('导入场景数据:', data);
                            alert('场景导入成功！');
                        } catch (error) {
                            alert('文件格式错误，请选择有效的JSON文件');
                        }
                    };
                    reader.readAsText(file);
                }
            };
            input.click();
        }

        // 更新场景计数
        function updateSceneCounts(change) {
            const totalCount = document.querySelector('.scene-count');
            const currentTotalCount = parseInt(totalCount.textContent.replace(/[()]/g, ''));
            totalCount.textContent = `(${Math.max(0, currentTotalCount + change)})`;
        }

        // 添加场景到列表
        function addSceneToList(sceneName) {
            const sceneList = document.querySelector('.scene-subsection');
            const newScene = document.createElement('div');
            newScene.className = 'tree-item scene-item';
            newScene.setAttribute('onclick', `selectScene(this, '${sceneName}')`);
            newScene.innerHTML = `
                <span class="scene-item-name">${sceneName}</span>
                <div class="tree-actions">
                    <button class="action-icon" title="编辑" onclick="event.stopPropagation(); editScene('${sceneName}')">✏</button>
                    <button class="action-icon" title="删除" onclick="event.stopPropagation(); deleteScene('${sceneName}')">🗑</button>
                    <button class="action-icon" title="更多" onclick="event.stopPropagation(); showSceneItemMenu(this, '${sceneName}')">⋯</button>
                </div>
            `;
            sceneList.appendChild(newScene);
            updateSceneCounts(1);
        }

        // 场景搜索功能
        function searchScenes(searchText) {
            const sceneItems = document.querySelectorAll('.scene-item');
            const searchLower = searchText.toLowerCase();
            
            sceneItems.forEach(item => {
                const sceneName = item.querySelector('.scene-item-name').textContent.toLowerCase();
                if (sceneName.includes(searchLower)) {
                    item.style.display = 'flex';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        // 展开/收起场景分组
        function toggleSceneSection(header) {
            const section = header.closest('.scene-section');
            const subsection = section.querySelector('.scene-subsection');
            const collapseBtn = header.querySelector('.collapse-btn span');
            
            if (subsection.style.display === 'none') {
                subsection.style.display = 'block';
                collapseBtn.textContent = '−';
                section.classList.remove('collapsed');
            } else {
                subsection.style.display = 'none';
                collapseBtn.textContent = '+';
                section.classList.add('collapsed');
            }
        }

        // 选择场景
        function selectScene(element, sceneName) {
            document.querySelectorAll('.scene-item').forEach(item => {
                item.classList.remove('selected');
            });
            
            element.classList.add('selected');
            console.log('选中场景:', sceneName);
            
            // 显示全部场景列表
            document.getElementById('new-scene-panel').style.display = 'none';
            document.getElementById('scenes-list-panel').style.display = 'flex';
        }

        // 编辑场景
        function editScene(sceneName) {
            const newName = prompt('请输入新的场景名称:', sceneName);
            if (newName && newName.trim() && newName !== sceneName) {
                console.log('重命名场景:', sceneName, '->', newName);
                const sceneItem = document.querySelector(`[onclick*="${sceneName}"]`);
                if (sceneItem) {
                    sceneItem.querySelector('.scene-item-name').textContent = newName;
                    sceneItem.setAttribute('onclick', sceneItem.getAttribute('onclick').replace(sceneName, newName));
                }
            }
        }

        // 删除场景
        function deleteScene(sceneName) {
            if (confirm(`确定要删除场景 "${sceneName}" 吗？`)) {
                console.log('删除场景:', sceneName);
                const sceneItem = document.querySelector(`[onclick*="${sceneName}"]`);
                if (sceneItem) {
                    sceneItem.remove();
                    updateSceneCounts(-1);
                }
            }
        }

        // 修改原有的编辑场景详情函数
        function editSceneDetails(id) {
            showEditScenePanel(id);
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('测试场景页面已加载');
        });
    </script>
</body>
</html>
