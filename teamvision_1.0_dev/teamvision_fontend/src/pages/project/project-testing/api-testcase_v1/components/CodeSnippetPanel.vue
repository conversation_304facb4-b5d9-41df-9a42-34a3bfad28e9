<template>
  <div class="code-snippet-panel">
    <div class="left-header">
      <div class="breadcrumb">
        <el-breadcrumb separator=">">
          <el-breadcrumb-item>代码片段</el-breadcrumb-item>
        </el-breadcrumb>
      </div>

      <div class="language-selector">
        <el-select v-model="selectedLanguage" @change="generateCodeSnippet" style="width: 100%">
          <el-option v-for="option in languageOptions" :key="option.value" :label="option.label" :value="option.value">
          </el-option>
        </el-select>
      </div>
    </div>

    <div class="code-section">
      <div class="section-title">
        <span>生成的代码</span>
        <div class="section-actions">
          <el-tooltip content="Format code" placement="top">
            <el-button icon="el-icon-document-copy" @click="formatCode" circle></el-button>
          </el-tooltip>
          <el-tooltip content="Download" placement="top">
            <el-button icon="el-icon-download" @click="downloadCode" circle></el-button>
          </el-tooltip>
          <el-tooltip content="Copy" placement="top">
            <el-button icon="el-icon-document" @click="copyCode" circle></el-button>
          </el-tooltip>
        </div>
      </div>

      <div class="code-editor">
        <el-input v-model="generatedCode" type="textarea" :rows="20" readonly class="code-textarea" />
      </div>
    </div>
  </div>
</template>

<script>
import {
  generateCodeSnippetApi,
  handleApiError,
  formatApiResponse
} from '@/api/apiTestCase'

export default {
  name: 'CodeSnippetPanel',
  props: {
    projectID: {
      type: Number,
      required: true
    },
    currentRequest: {
      type: Object,
      default: () => ({
        method: 'GET',
        url: '',
        id: null
      })
    },
    selectedEnvironmentId: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      loading: false,
      selectedLanguage: 'curl',
      generatedCode: '',
      languageOptions: [
        { label: 'cURL', value: 'curl' },
        { label: 'Python - Requests', value: 'python' },
        { label: 'JavaScript - Fetch', value: 'javascript' },
        { label: 'Java - OkHttp', value: 'java' },
        { label: 'Go - HTTP', value: 'go' }
      ]
    }
  },
  computed: {
    codeLines() {
      return this.generatedCode.split('\n');
    }
  },
  watch: {
    currentRequest: {
      handler() {
        this.generateCodeSnippet();
      },
      deep: true
    }
  },
  mounted() {
    this.generateCodeSnippet();
  },
  methods: {
    // 生成代码片段
    async generateCodeSnippet() {
      if (!this.currentRequest.id || !this.currentRequest.url) {
        this.generatedCode = '// 请先选择一个有效的测试用例'
        return
      }

      this.loading = true
      try {
        const requestData = {
          language: this.selectedLanguage,
          environment_id: this.selectedEnvironmentId
        }

        const response = await generateCodeSnippetApi(
          this.projectID,
          this.currentRequest.id,
          requestData
        )

        const result = formatApiResponse(response)
        if (result.success) {
          this.generatedCode = result.data.code_snippet || ''
        } else {
          this.$message.error(result.message || '生成代码片段失败')
          this.generateFallbackCode()
        }
      } catch (error) {
        this.$message.error(handleApiError(error))
        this.generateFallbackCode()
      } finally {
        this.loading = false
      }
    },

    // 生成备用代码（当API调用失败时）
    generateFallbackCode() {
      const { method, url } = this.currentRequest
      if (!url) {
        this.generatedCode = '// 请输入有效的URL'
        return
      }

      switch (this.selectedLanguage) {
        case 'curl':
          this.generatedCode = `curl -X ${method} \\\n  "${url}"`
          break
        case 'python':
          this.generatedCode = `import requests\n\nresponse = requests.${method.toLowerCase()}("${url}")\nprint(response.text)`
          break
        case 'javascript':
          this.generatedCode = `fetch("${url}", {\n  method: "${method}"\n})\n.then(response => response.text())\n.then(data => console.log(data))`
          break
        case 'java':
          this.generatedCode = `// Java OkHttp code for ${method} ${url}\n// Please use the API to generate complete code`
          break
        case 'go':
          this.generatedCode = `// Go HTTP code for ${method} ${url}\n// Please use the API to generate complete code`
          break
        default:
          this.generatedCode = `// ${this.selectedLanguage} code for ${method} ${url}`
      }
    },
    // 格式化代码
    formatCode() {
      if (!this.generatedCode) return

      try {
        // 简单的代码格式化
        let formatted = this.generatedCode
          .replace(/;/g, ';\n')
          .replace(/\{/g, '{\n')
          .replace(/\}/g, '\n}')
          .replace(/,/g, ',\n')

        // 移除多余的空行
        formatted = formatted.replace(/\n\s*\n/g, '\n')

        this.generatedCode = formatted
        this.$message.success('代码格式化完成')
      } catch (error) {
        this.$message.error('代码格式化失败')
      }
    },

    // 下载代码
    downloadCode() {
      if (!this.generatedCode) {
        this.$message.warning('没有可下载的代码')
        return
      }

      const blob = new Blob([this.generatedCode], { type: 'text/plain' })
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `${this.currentRequest.name || 'request'}_${this.selectedLanguage}.${this.getFileExtension()}`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      window.URL.revokeObjectURL(url)
      this.$message.success('代码下载成功')
    },

    // 复制代码
    copyCode() {
      if (!this.generatedCode) {
        this.$message.warning('没有可复制的代码')
        return
      }

      navigator.clipboard.writeText(this.generatedCode).then(() => {
        this.$message.success('代码已复制到剪贴板')
      }).catch(err => {
        this.$message.error('复制失败: ' + err.message)
      })
    },

    // 获取文件扩展名
    getFileExtension() {
      const extensions = {
        'curl': 'sh',
        'javascript': 'js',
        'python': 'py',
        'java': 'java',
        'go': 'go'
      }
      return extensions[this.selectedLanguage] || 'txt'
    },

    // 刷新代码片段
    refreshCodeSnippet() {
      this.generateCodeSnippet()
    }
  }
}
</script>

<style scoped>
.code-snippet-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.left-header {
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.breadcrumb {
  font-size: 12px;
  color: #666;
  margin-bottom: 12px;
}

.language-selector {
  margin-bottom: 16px;
}


.code-section {
  flex: 1;
  padding: 16px;
  display: flex;
  flex-direction: column;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.section-actions {
  display: flex;
  gap: 8px;
}

.code-editor {
  flex: 1;
}

.code-textarea>>>.el-textarea__inner {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 10px;
  line-height: 1.5;
  background: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  color: #333;
}

.code-textarea>>>.el-textarea__inner:focus {
  border-color: #409eff;
}
</style>
