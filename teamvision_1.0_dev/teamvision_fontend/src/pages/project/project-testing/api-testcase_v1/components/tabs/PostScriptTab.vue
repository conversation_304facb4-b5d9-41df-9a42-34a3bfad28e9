<template>
  <div class="post-script-tab">
    <div class="script-header">
      <div class="header-left">
        <h4 class="section-title">Post-response Script</h4>
        <p class="section-description">Execute JavaScript after receiving the response</p>
      </div>
      <div class="header-right">
        <el-button type="text" @click="insertSnippet" class="snippet-btn">
          <i class="el-icon-document-add"></i> Snippets
        </el-button>
        <el-button type="text" @click="clearScript" class="clear-btn">
          <i class="el-icon-delete"></i> Clear
        </el-button>
      </div>
    </div>

    <div class="script-editor">
      <el-input v-model="scriptContent" type="textarea" :rows="15" placeholder="// Write your post-response script here
// Examples:
// pm.test('Status code is 200', function () {
//     pm.response.to.have.status(200);
// });
// 
// pm.test('Response time is less than 200ms', function () {
//     pm.expect(pm.response.responseTime).to.be.below(200);
// });
//
// var jsonData = pm.response.json();
// pm.environment.set('user_id', jsonData.data.user.id);" class="script-textarea" />
    </div>

    <!-- 代码片段选择器 -->
    <el-dialog title="Test & Script Snippets" :visible.sync="showSnippets" width="600px" class="snippets-dialog">
      <div class="snippets-list">
        <div v-for="(snippet, index) in codeSnippets" :key="index" class="snippet-item" @click="selectSnippet(snippet)">
          <div class="snippet-header">
            <span class="snippet-name">{{ snippet.name }}</span>
            <span class="snippet-desc">{{ snippet.description }}</span>
          </div>
          <pre class="snippet-code">{{ snippet.code }}</pre>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showSnippets = false">Close</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'PostScriptTab',
  data() {
    return {
      scriptContent: '',
      showSnippets: false,
      codeSnippets: [
        {
          name: 'Status Code Test',
          description: 'Test if status code is 200',
          code: `pm.test("Status code is 200", function () {
    pm.response.to.have.status(200);
});`
        },
        {
          name: 'Response Time Test',
          description: 'Test if response time is acceptable',
          code: `pm.test("Response time is less than 200ms", function () {
    pm.expect(pm.response.responseTime).to.be.below(200);
});`
        },
        {
          name: 'JSON Response Test',
          description: 'Test if response is valid JSON',
          code: `pm.test("Response is valid JSON", function () {
    pm.response.to.be.json;
});`
        },
        {
          name: 'Extract JSON Value',
          description: 'Extract value from JSON response',
          code: `var jsonData = pm.response.json();
pm.environment.set("user_id", jsonData.data.user.id);`
        },
        {
          name: 'Header Test',
          description: 'Test response headers',
          code: `pm.test("Content-Type is application/json", function () {
    pm.expect(pm.response.headers.get("Content-Type")).to.include("application/json");
});`
        },
        {
          name: 'Response Body Contains',
          description: 'Test if response body contains text',
          code: `pm.test("Body matches string", function () {
    pm.expect(pm.response.text()).to.include("success");
});`
        },
        {
          name: 'Set Environment Variable',
          description: 'Set variable from response data',
          code: `var responseJson = pm.response.json();
pm.environment.set("token", responseJson.access_token);`
        },
        {
          name: 'Multiple Assertions',
          description: 'Multiple test assertions',
          code: `pm.test("Verify response structure", function () {
    var jsonData = pm.response.json();
    
    pm.expect(jsonData).to.have.property('success');
    pm.expect(jsonData.success).to.be.true;
    pm.expect(jsonData.data).to.be.an('object');
});`
        }
      ]
    }
  },
  methods: {
    insertSnippet() {
      this.showSnippets = true;
    },
    selectSnippet(snippet) {
      const cursorPos = this.$refs?.textarea?.$el?.querySelector('textarea')?.selectionStart || this.scriptContent.length;
      const before = this.scriptContent.substring(0, cursorPos);
      const after = this.scriptContent.substring(cursorPos);

      this.scriptContent = before + (before ? '\n\n' : '') + snippet.code + (after ? '\n\n' : '') + after;
      this.showSnippets = false;
      this.$emit('script-change', this.scriptContent);
    },
    clearScript() {
      this.$confirm('Are you sure you want to clear the script?', 'Warning', {
        confirmButtonText: 'Clear',
        cancelButtonText: 'Cancel',
        type: 'warning'
      }).then(() => {
        this.scriptContent = '';
        this.$emit('script-change', this.scriptContent);
      }).catch(() => { });
    }
  },
  watch: {
    scriptContent() {
      this.$emit('script-change', this.scriptContent);
    }
  }
}
</script>

<style scoped>
.post-script-tab {
  padding: 0;
}

.script-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.header-left {
  flex: 1;
}

.header-right {
  display: flex;
  gap: 6px;
  flex-shrink: 0;
}

.section-title {
  font-size: 13px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 4px 0;
}

.section-description {
  font-size: 11px;
  color: #909399;
  margin: 0;
  line-height: 1.4;
}

.snippet-btn,
.clear-btn {
  font-size: 11px;
  padding: 2px 6px;
  height: 22px;
  line-height: 18px;
}

.snippet-btn {
  color: #1890ff;
}

.clear-btn {
  color: #f56c6c;
}

.snippet-btn:hover {
  color: #40a9ff;
  background-color: rgba(24, 144, 255, 0.06);
}

.clear-btn:hover {
  color: #f78989;
  background-color: rgba(245, 108, 108, 0.06);
}

.script-editor {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  overflow: hidden;
}

.script-textarea>>>.el-textarea__inner {
  border: none;
  border-radius: 0;
  font-family: 'Monaco', 'Menlo', 'Courier New', monospace;
  font-size: 11px;
  line-height: 1.6;
  padding: 12px;
  resize: vertical;
  min-height: 300px;
  background: #fafafa;
}

.script-textarea>>>.el-textarea__inner:focus {
  background: white;
}

/* 代码片段对话框 */
.snippets-list {
  max-height: 400px;
  overflow-y: auto;
}

.snippet-item {
  padding: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.snippet-item:hover {
  border-color: #1890ff;
  background-color: rgba(24, 144, 255, 0.02);
}

.snippet-item:last-child {
  margin-bottom: 0;
}

.snippet-header {
  display: flex;
  flex-direction: column;
  gap: 2px;
  margin-bottom: 8px;
}

.snippet-name {
  font-weight: 600;
  color: #303133;
  font-size: 12px;
}

.snippet-desc {
  font-size: 10px;
  color: #909399;
}

.snippet-code {
  font-family: 'Monaco', 'Menlo', 'Courier New', monospace;
  font-size: 10px;
  line-height: 1.4;
  background: #f5f5f5;
  padding: 6px 8px;
  border-radius: 3px;
  margin: 0;
  color: #606266;
  white-space: pre-wrap;
  word-break: break-all;
}

/* 滚动条优化 */
.script-textarea>>>.el-textarea__inner::-webkit-scrollbar,
.snippets-list::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.script-textarea>>>.el-textarea__inner::-webkit-scrollbar-track,
.snippets-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.script-textarea>>>.el-textarea__inner::-webkit-scrollbar-thumb,
.snippets-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.script-textarea>>>.el-textarea__inner::-webkit-scrollbar-thumb:hover,
.snippets-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .script-header {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .header-right {
    justify-content: flex-start;
  }

  .script-textarea>>>.el-textarea__inner {
    min-height: 250px;
    font-size: 10px;
  }
}
</style>
