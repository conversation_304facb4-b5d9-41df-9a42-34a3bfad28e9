<template>
  <div class="api-test-case-container" :style="{ height: containerHeight + 'px' }">
    <el-container>
      <!-- 左侧导航栏 -->
      <el-aside width="32px" class="nav-sidebar">
        <div v-for="nav in navItems" :key="nav.key" :class="['nav-icon', { active: activeNavTab === nav.key }]"
          :title="nav.title" @click="switchNavTab(nav.key)">
          <i :class="nav.iconClass"></i>
        </div>
      </el-aside>

      <!-- 左侧功能面板 -->
      <el-aside :width="leftPanelWidth + 'px'" class="left-panel">
        <!-- Collections 选项卡 -->
        <collections-panel v-if="activeNavTab === 'collections'" :projectID="projectID" />

        <!-- Environments 选项卡 -->
        <environments-panel v-if="activeNavTab === 'environments'" :projectID="projectID" ref="environmentsPanel" />

        <!-- History 选项卡 -->
        <history-panel v-if="activeNavTab === 'history'" :projectID="projectID"
          @update-request="updateCurrentRequest" />

        <!-- Code snippet 选项卡 -->
        <code-snippet-panel v-if="activeNavTab === 'code'" :current-request="currentRequest" :projectID="projectID" />

        <!-- Scenes 选项卡 -->
        <test-scene-panel v-if="activeNavTab === 'scenes'" :projectID="projectID" />
      </el-aside>

      <!-- 右侧主工作区 -->
      <el-main class="right-panel">
        <!-- API测试用例内容 -->
        <api-case-content v-if="activeNavTab !== 'scenes'" :projectID="projectID" :current-request="currentRequest"
          @update-request="updateCurrentRequest" />

        <!-- 测试场景内容 -->
        <test-scene-content v-if="activeNavTab === 'scenes'" :projectID="projectID" />
      </el-main>
    </el-container>
  </div>
</template>

<script>
import ApiCaseContent from "./ApiCaseContent.vue";
import CollectionsPanel from "./components/CollectionsPanel.vue";
import EnvironmentsPanel from "./components/EnvironmentsPanel.vue";
import HistoryPanel from "./components/HistoryPanel.vue";
import CodeSnippetPanel from "./components/CodeSnippetPanel.vue";
import TestScenePanel from "./components/TestScenePanel.vue";
import TestSceneContent from "./ApiTestSceneContent.vue";
import { mapState } from "vuex";

export default {
  name: "ApiTestCase",
  props: {
    projectID: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      activeNavTab: 'collections',
      leftPanelWidth: 280,
      currentRequest: {
        method: 'GET',
        url: 'https://www.baidu.com/',
        name: 'baidu'
      },
      navItems: [
        { key: 'collections', title: 'Collections', iconClass: 'el-icon-folder' },
        { key: 'environments', title: 'Environments', iconClass: 'el-icon-document' },
        { key: 'history', title: 'History', iconClass: 'el-icon-time' },
        { key: 'code', title: 'Code', iconClass: 'el-icon-edit-outline' },
        { key: 'scenes', title: 'Scenes', iconClass: 'el-icon-s-operation' }
      ]
    }
  },
  computed: {
    ...mapState(['appBodyHeight']),
    containerHeight() {
      return this.appBodyHeight
    }
  },
  methods: {
    switchNavTab(tabKey) {
      this.activeNavTab = tabKey;

      // 切换到collections时，确保有默认请求数据
      if (tabKey === 'collections' && !this.currentRequest.url) {
        this.currentRequest = {
          method: 'GET',
          url: 'https://www.baidu.com/',
          name: 'baidu'
        };
      }
    },
    updateCurrentRequest(requestData) {
      this.currentRequest = { ...this.currentRequest, ...requestData };
    }
  },
  created() {
    // 初始化逻辑
  },
  components: {
    ApiCaseContent,
    CollectionsPanel,
    EnvironmentsPanel,
    HistoryPanel,
    CodeSnippetPanel,
    TestScenePanel,
    TestSceneContent
  }
}
</script>

<style scoped>
.api-test-case-container {
  height: 100vh;
  background: white;
}

.api-test-case-container>>>.el-container {
  height: 100%;
}

/* 左侧导航栏 */
.nav-sidebar {
  background: #f8f9fa;
  border-right: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 0;
}

.nav-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  cursor: pointer;
  margin-bottom: 8px;
  color: #666666;
  font-size: 16px;
  transition: all 0.2s ease;
}

.nav-icon:hover {
  background: #e9ecef;
  color: #333;
}

.nav-icon.active {
  background: #409EFF;
  color: white;
}

/* 左侧功能面板 */
.left-panel {
  background: white;
  border-right: 1px solid #e0e0e0;
  transition: width 0.3s ease;
}

.left-panel>>>.el-aside {
  background: white;
}

/* 右侧主工作区 */
.right-panel {
  padding: 0;
  background: white;
}

.right-panel>>>.el-main {
  padding: 0;
  background: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nav-sidebar {
    width: 100% !important;
    height: 60px;
    flex-direction: row;
    padding: 4px 16px;
  }

  .nav-icon {
    margin-bottom: 0;
    margin-right: 4px;
  }

  .left-panel {
    width: 100% !important;
    height: 200px;
  }
}
</style>
