<template>
  <div class="test-scene-content">
    <!-- 场景列表视图 -->
    <div v-if="currentView === 'list'" class="scene-list-view">
      <scene-list :project-id="projectID" :scenes="scenes" :loading="loading" @create-scene="showCreateScenePanel"
        @edit-scene="showEditScenePanel" @delete-scene="handleDeleteScene" @copy-scene="handleCopyScene"
        @execute-scene="handleExecuteScene" @refresh="loadScenes" />
    </div>

    <!-- 新建场景视图 -->
    <div v-if="currentView === 'create'" class="scene-form-view">
      <scene-form :project-id="projectID" mode="create" @save="handleSaveScene" @cancel="showListView" />
    </div>

    <!-- 编辑场景视图 -->
    <div v-if="currentView === 'edit'" class="scene-form-view">
      <scene-form :project-id="projectID" :scene-data="currentEditScene" mode="edit" @save="handleUpdateScene"
        @cancel="showListView" />
    </div>
  </div>
</template>

<script>
import {
  getApiTestScenesApi,
  createApiTestSceneApi,
  updateApiTestSceneApi,
  deleteApiTestSceneApi,
  copyApiTestSceneApi,
  executeApiTestSceneApi,
  handleApiError,
  formatApiResponse
} from '@/api/apiTestCase'
import SceneList from './components/scene/SceneList.vue'
import SceneForm from './components/scene/SceneForm.vue'

export default {
  name: 'TestSceneContent',
  components: {
    SceneList,
    SceneForm
  },
  props: {
    projectID: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      // 视图状态
      currentView: 'list', // list, create, edit

      // 场景数据
      scenes: [],
      loading: false,

      // 编辑状态
      currentEditScene: null
    }
  },
  mounted() {
    this.loadScenes()
    // 监听来自TestScenePanel的事件
    this.$root.$on('scene-create-requested', this.showCreateScenePanel)
    this.$root.$on('scene-edit-requested', this.showEditScenePanel)
    this.$root.$on('scene-delete-requested', this.handleDeleteScene)
  },
  beforeDestroy() {
    // 清理事件监听
    this.$root.$off('scene-create-requested', this.showCreateScenePanel)
    this.$root.$off('scene-edit-requested', this.showEditScenePanel)
    this.$root.$off('scene-delete-requested', this.handleDeleteScene)
  },
  methods: {
    // 加载场景列表
    async loadScenes() {
      this.loading = true
      try {
        const response = await getApiTestScenesApi(this.projectID)

        if (response.data.code) {
          this.scenes = response.data.result || []
          // 通知TestScenePanel更新场景数据
          this.$root.$emit('scenes-loaded', this.scenes)
        } else {
          this.$message.error(response.data.message || '加载场景列表失败')
        }
      } catch (error) {
        this.$message.error(handleApiError(error))
      } finally {
        this.loading = false
      }
    },

    // 视图切换
    showListView() {
      this.currentView = 'list'
      this.currentEditScene = null
    },

    showCreateScenePanel() {
      this.currentView = 'create'
    },

    showEditScenePanel(scene) {
      this.currentView = 'edit'
      this.currentEditScene = scene
    },

    // 场景操作
    async handleSaveScene(sceneData) {
      try {
        const response = await createApiTestSceneApi(this.projectID, sceneData)
        if (response.data.code) {
          this.$message.success('场景创建成功')
          this.showListView()
          this.loadScenes()
        } else {
          this.$message.error(response.data.message || '创建场景失败')
        }
      } catch (error) {
        this.$message.error(handleApiError(error))
      }
    },

    async handleUpdateScene(sceneData) {
      try {
        const response = await updateApiTestSceneApi(
          this.projectID,
          this.currentEditScene.id,
          sceneData
        )
        if (response.data.code) {
          this.$message.success('场景更新成功')
          this.showListView()
          this.loadScenes()
        } else {
          this.$message.error(response.data.message || '更新场景失败')
        }
      } catch (error) {
        this.$message.error(handleApiError(error))
      }
    },

    async handleDeleteScene(scene) {
      try {
        await this.$confirm(`确定要删除场景 "${scene.name}" 吗？`, '确认删除', {
          type: 'warning'
        })

        const response = await deleteApiTestSceneApi(this.projectID, scene.id)

        if (response.data.code) {
          this.$message.success('场景删除成功')
          this.loadScenes()
        } else {
          this.$message.error(response.data.message || '删除场景失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error(handleApiError(error))
        }
      }
    },

    async handleCopyScene(scene) {
      try {
        const response = await copyApiTestSceneApi(this.projectID, scene.id, {
          name: `${scene.name} - 副本`
        })
        if (response.data.code) {
          this.$message.success('场景复制成功')
          this.loadScenes()
        } else {
          this.$message.error(response.data.message || '复制场景失败')
        }
      } catch (error) {
        this.$message.error(handleApiError(error))
      }
    },

    async handleExecuteScene(scene) {
      try {
        const response = await executeApiTestSceneApi(this.projectID, scene.id)
        if (response.data.code) {
          this.$message.success('场景执行已启动')
          // 可以跳转到执行历史页面或显示执行状态
        } else {
          this.$message.error(response.data.message || '执行场景失败')
        }
      } catch (error) {
        this.$message.error(handleApiError(error))
      }
    }
  }
}
</script>

<style scoped>
.test-scene-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
}

.scene-list-view,
.scene-form-view {
  flex: 1;
  display: flex;
  flex-direction: column;
}
</style>