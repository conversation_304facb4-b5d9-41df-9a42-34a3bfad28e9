// 简单的构建测试脚本
const fs = require('fs');
const path = require('path');

console.log('检查关键文件是否存在...');

const filesToCheck = [
  'teamvision_fontend/src/api/apiTest.js',
  'teamvision_fontend/src/pages/project/project-testing/api-testcase/ApiTestCaseContent.vue',
  'teamvision_fontend/src/pages/project/project-testing/api-testcase/styles/api-test.less'
];

let allFilesExist = true;

filesToCheck.forEach(file => {
  const fullPath = path.join(__dirname, file);
  if (fs.existsSync(fullPath)) {
    console.log(`✅ ${file} 存在`);
  } else {
    console.log(`❌ ${file} 不存在`);
    allFilesExist = false;
  }
});

if (allFilesExist) {
  console.log('\n✅ 所有关键文件都存在，构建应该可以成功');
} else {
  console.log('\n❌ 有文件缺失，需要检查');
}

// 检查导入路径
console.log('\n检查导入路径...');
const contentFile = path.join(__dirname, 'teamvision_fontend/src/pages/project/project-testing/api-testcase/ApiTestCaseContent.vue');
if (fs.existsSync(contentFile)) {
  const content = fs.readFileSync(contentFile, 'utf8');
  if (content.includes('../../../../api/apiTest.js')) {
    console.log('✅ apiTest.js 导入路径正确');
  } else {
    console.log('❌ apiTest.js 导入路径可能有问题');
  }
}
