<template>
  <div class="authorization-tab">
    <div class="auth-type-selector">
      <div class="section-header">
        <h4 class="section-title">Authorization Type</h4>
      </div>
      <el-select v-model="authType" @change="onAuthTypeChange" class="auth-type-select">
        <el-option label="No Auth" value="none"></el-option>
        <el-option label="Bearer Token" value="bearer"></el-option>
        <el-option label="Basic Auth" value="basic"></el-option>
        <el-option label="API Key" value="apikey"></el-option>
        <el-option label="OAuth 2.0" value="oauth2"></el-option>
      </el-select>
    </div>

    <div class="auth-content">
      <!-- No Auth -->
      <div v-if="authType === 'none'" class="empty-state">
        <i class="el-icon-unlock"></i>
        <p>This request does not use any authorization</p>
      </div>

      <!-- Bear<PERSON> Token -->
      <div v-else-if="authType === 'bearer'" class="bearer-section">
        <div class="auth-form">
          <div class="form-item">
            <label class="form-label">Token</label>
            <el-input v-model="bearerToken" placeholder="Enter bearer token" type="password" show-password
              class="token-input" />
          </div>
          <div class="form-item">
            <label class="form-label">Prefix</label>
            <el-input v-model="bearerPrefix" placeholder="Bearer" class="prefix-input" />
          </div>
        </div>
      </div>

      <!-- Basic Auth -->
      <div v-else-if="authType === 'basic'" class="basic-section">
        <div class="auth-form">
          <div class="form-item">
            <label class="form-label">Username</label>
            <el-input v-model="basicAuth.username" placeholder="Enter username" />
          </div>
          <div class="form-item">
            <label class="form-label">Password</label>
            <el-input v-model="basicAuth.password" placeholder="Enter password" type="password" show-password />
          </div>
        </div>
      </div>

      <!-- API Key -->
      <div v-else-if="authType === 'apikey'" class="apikey-section">
        <div class="auth-form">
          <div class="form-item">
            <label class="form-label">Key</label>
            <el-input v-model="apiKey.key" placeholder="Enter key name" />
          </div>
          <div class="form-item">
            <label class="form-label">Value</label>
            <el-input v-model="apiKey.value" placeholder="Enter key value" type="password" show-password />
          </div>
          <div class="form-item">
            <label class="form-label">Add to</label>
            <el-select v-model="apiKey.location" class="location-select">
              <el-option label="Header" value="header"></el-option>
              <el-option label="Query Params" value="query"></el-option>
            </el-select>
          </div>
        </div>
      </div>

      <!-- OAuth 2.0 -->
      <div v-else-if="authType === 'oauth2'" class="oauth2-section">
        <div class="auth-form">
          <div class="form-item">
            <label class="form-label">Access Token</label>
            <el-input v-model="oauth2.accessToken" placeholder="Enter access token" type="password" show-password />
          </div>
          <div class="form-item">
            <label class="form-label">Token Type</label>
            <el-select v-model="oauth2.tokenType" class="token-type-select">
              <el-option label="Bearer" value="Bearer"></el-option>
              <el-option label="MAC" value="MAC"></el-option>
            </el-select>
          </div>
          <div class="oauth2-actions">
            <el-button type="text" class="get-token-btn">
              <i class="el-icon-refresh"></i> Get New Access Token
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AuthorizationTab',
  data() {
    return {
      authType: 'none',
      bearerToken: '',
      bearerPrefix: 'Bearer',
      basicAuth: {
        username: '',
        password: ''
      },
      apiKey: {
        key: '',
        value: '',
        location: 'header'
      },
      oauth2: {
        accessToken: '',
        tokenType: 'Bearer'
      }
    }
  },
  methods: {
    onAuthTypeChange() {
      this.$emit('auth-type-change', {
        type: this.authType,
        config: this.getAuthConfig()
      });
    },
    getAuthConfig() {
      switch (this.authType) {
        case 'bearer':
          return { token: this.bearerToken, prefix: this.bearerPrefix };
        case 'basic':
          return this.basicAuth;
        case 'apikey':
          return this.apiKey;
        case 'oauth2':
          return this.oauth2;
        default:
          return null;
      }
    }
  }
}
</script>

<style scoped>
.authorization-tab {
  padding: 0;
}

.auth-type-selector {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.section-header {
  margin-bottom: 8px;
}

.section-title {
  font-size: 13px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.auth-type-select {
  width: 200px;
}

.auth-content {
  min-height: 200px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #909399;
  background: #fafafa;
  border-radius: 4px;
}

.empty-state i {
  font-size: 36px;
  margin-bottom: 12px;
  color: #dcdfe6;
}

.empty-state p {
  font-size: 13px;
  margin: 0;
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-width: 500px;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-label {
  font-size: 12px;
  font-weight: 600;
  color: #606266;
  margin: 0;
}

.token-input,
.prefix-input {
  width: 100%;
}

.location-select,
.token-type-select {
  width: 150px;
}

.oauth2-actions {
  margin-top: 8px;
}

.get-token-btn {
  color: #1890ff;
  font-size: 11px;
  padding: 2px 6px;
  height: 22px;
  line-height: 18px;
}

.get-token-btn:hover {
  color: #40a9ff;
  background-color: rgba(24, 144, 255, 0.06);
}

.authorization-tab>>>.el-input--mini .el-input__inner {
  height: 28px;
  line-height: 28px;
  font-size: 11px;
  border-radius: 4px;
  padding: 0 8px;
}

.authorization-tab>>>.el-select--mini .el-input__inner {
  height: 28px;
  line-height: 28px;
  font-size: 11px;
  padding: 0 8px;
}

.authorization-tab>>>.el-input--mini .el-input__suffix {
  right: 6px;
}

.authorization-tab>>>.el-input--mini .el-input__icon {
  line-height: 28px;
}

/* 响应式优化 */
@media (max-width: 768px) {

  .auth-type-select,
  .location-select,
  .token-type-select {
    width: 100%;
  }

  .auth-form {
    max-width: none;
  }
}
</style>
