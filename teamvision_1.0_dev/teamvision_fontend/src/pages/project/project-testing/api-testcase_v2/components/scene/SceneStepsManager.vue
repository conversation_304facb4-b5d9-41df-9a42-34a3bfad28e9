<template>
  <div class="scene-steps-manager">
    <!-- 步骤头部 -->
    <div class="steps-header">
      <span class="steps-count">共 {{ steps.length }} 个步骤</span>
      <div class="steps-actions">
        <el-button size="small" icon="el-icon-refresh" @click="refreshSteps">刷新</el-button>
      </div>
    </div>

    <!-- 步骤列表 -->
    <div class="steps-container">
      <draggable v-model="localSteps" group="steps" @start="onDragStart" @end="onDragEnd" handle=".step-drag-handle"
        animation="200" ghost-class="step-ghost" chosen-class="step-chosen">
        <div v-for="(step, index) in localSteps" :key="step.id || index"
          :class="['step-item', { disabled: !step.is_enabled }]">
          <!-- 步骤选择和序号 -->
          <div class="step-controls">
            <el-checkbox v-model="step.is_enabled" @change="updateStep(step)"></el-checkbox>
            <span class="step-number">{{ index + 1 }}</span>
            <div class="step-drag-handle">
              <i class="el-icon-s-unfold"></i>
            </div>
          </div>

          <!-- 步骤内容 -->
          <div class="step-content">
            <!-- 步骤类型标签 -->
            <el-tag :type="getStepTypeTagType(step.step_type)" size="small" class="step-type-badge">
              {{ getStepTypeText(step.step_type) }}
            </el-tag>

            <!-- API步骤 -->
            <template v-if="step.step_type === 'api'">
              <el-tag :type="getMethodTagType(step.method)" size="mini" class="step-method-badge">
                {{ step.method }}
              </el-tag>
              <span class="step-description">{{ step.step_name || step.description }}</span>
            </template>

            <!-- 等待步骤 -->
            <template v-if="step.step_type === 'wait'">
              <span class="step-description">等待(ms): {{ step.wait_time || 0 }}</span>
              <span class="step-note">{{ step.step_name || step.description }}</span>
            </template>

            <!-- 控制器步骤 -->
            <template v-if="step.step_type === 'controller'">
              <span class="step-description">{{ step.condition || step.step_name }}</span>
              <span class="step-operator">{{ step.operator || '等于' }}</span>
              <span class="step-value">{{ step.expected_value || '' }}</span>
              <span class="step-note">{{ step.description }}</span>
            </template>

            <!-- 测试用例步骤 -->
            <template v-if="step.step_type === 'case'">
              <el-tag :type="getMethodTagType(step.method)" size="mini" class="step-method-badge">
                {{ step.method }}
              </el-tag>
              <span class="step-description">{{ step.step_name || step.description }}</span>
            </template>
          </div>

          <!-- 步骤操作 -->
          <div class="step-actions">
            <el-button type="text" size="mini" icon="el-icon-delete" @click="deleteStep(step, index)"
              title="删除"></el-button>
            <el-dropdown @command="handleStepCommand" trigger="click">
              <el-button type="text" size="mini" icon="el-icon-more" title="更多"></el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item :command="{ action: 'copy', step, index }">复制</el-dropdown-item>
                <el-dropdown-item :command="{ action: 'run', step, index }">单独执行</el-dropdown-item>
                <el-dropdown-item :command="{ action: 'disable', step, index }">
                  {{ step.is_enabled ? '禁用' : '启用' }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
      </draggable>

      <!-- 添加步骤按钮 -->
      <el-dropdown @command="handleAddStepCommand" trigger="click" placement="top-start"
        class="add-step-dropdown-wrapper">
        <div class="add-step-button">
          <i class="el-icon-plus"></i>
          <span>添加步骤</span>
        </div>
        <el-dropdown-menu slot="dropdown" class="add-step-dropdown">
          <el-dropdown-item disabled class="dropdown-header">
            <span>请求</span>
          </el-dropdown-item>
          <el-dropdown-item command="import_request">
            <i class="el-icon-upload2"></i>
            导入系统请求
          </el-dropdown-item>
          <el-dropdown-item divided disabled class="dropdown-header">
            <span>逻辑控制</span>
          </el-dropdown-item>
          <el-dropdown-item command="loop_controller">
            <i class="el-icon-refresh"></i>
            循环控制器
          </el-dropdown-item>
          <el-dropdown-item command="condition_controller">
            <i class="el-icon-s-operation"></i>
            条件控制器
          </el-dropdown-item>
          <el-dropdown-item command="if_controller">
            <i class="el-icon-question"></i>
            仅一次控制器
          </el-dropdown-item>
          <el-dropdown-item divided disabled class="dropdown-header">
            <span>其他</span>
          </el-dropdown-item>
          <el-dropdown-item command="script_step">
            <i class="el-icon-document"></i>
            脚本操作
          </el-dropdown-item>
          <el-dropdown-item command="wait_step">
            <i class="el-icon-time"></i>
            等待时间
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>

    <!-- 添加/编辑步骤抽屉 -->
    <Drawer :title="drawerTitle" v-model="showStepDrawer" :width="50" :mask="false" :transfer="false" :inner="true"
      @on-close="resetStepForm">

      <!-- 导入系统请求界面 -->
      <div v-if="currentStepCommand === 'import_request'" class="import-request-container">
        <div class="import-header">
          <div class="import-tabs">
            <el-tabs v-model="activeImportTab">
              <!-- 接口列表 -->
              <el-tab-pane label="接口" name="api">
                <div class="import-content api-import-layout">
                  <!-- 主要内容区域：左侧目录树 + 右侧接口列表 -->
                  <div class="api-main-content">
                    <!-- 左侧接口目录树 -->
                    <div class="api-tree-panel">
                      <div class="tree-header">
                        <span>接口目录</span>
                      </div>
                      <div class="tree-content" v-loading="treeLoading">
                        <el-tree ref="apiTree" :data="apiTreeData" :props="treeProps" node-key="id"
                          :expand-on-click-node="false" :highlight-current="true" @node-click="handleTreeNodeClick"
                          :filter-node-method="filterTreeNode">
                          <span class="custom-tree-node" slot-scope="{ node, data }">
                            <i v-if="data.is_folder" class="el-icon-folder tree-folder-icon"></i>
                            <el-tag v-else-if="data.method" :type="getMethodTagType(data.method)" size="mini"
                              class="tree-method-tag">
                              {{ data.method }}
                            </el-tag>
                            <span class="tree-node-label">{{ node.label }}</span>
                            <span class="tree-node-count" v-if="data.is_folder && data.case_count > 0">
                              {{ data.case_count }}/{{ data.case_count }}
                            </span>
                          </span>
                        </el-tree>
                      </div>
                    </div>

                    <!-- 右侧接口列表 -->
                    <div class="api-list-panel">
                      <div class="list-header">
                        <span>{{ currentCollectionName || '接口' }} ({{ filteredApiList.length }})</span>
                        <div class="list-actions">
                          <el-button size="mini" @click="selectAllApis">全选</el-button>
                          <el-button size="mini" @click="clearApiSelection">筛选</el-button>
                          <el-button size="mini" icon="el-icon-refresh" @click="refreshApiList"></el-button>
                        </div>
                      </div>
                      <div class="list-content">
                        <el-table ref="apiTable" :data="filteredApiList" @selection-change="handleSelectionChange"
                          max-height="350" size="small">
                          <el-table-column type="selection" width="40"></el-table-column>
                          <el-table-column prop="id" label="ID" width="60" sortable></el-table-column>
                          <el-table-column prop="name" label="接口名称" min-width="120" show-overflow-tooltip>
                            <template slot-scope="scope">
                              <span>{{ scope.row.name }}</span>
                            </template>
                          </el-table-column>
                          <el-table-column prop="method" label="请求类型" width="80">
                            <template slot-scope="scope">
                              <el-tag :type="getMethodTagType(scope.row.method)" size="mini">
                                {{ scope.row.method }}
                              </el-tag>
                            </template>
                          </el-table-column>
                          <el-table-column prop="url" label="路径" min-width="180" show-overflow-tooltip>
                            <template slot-scope="scope">
                              <span class="request-path">{{ scope.row.url || scope.row.path }}</span>
                            </template>
                          </el-table-column>
                          <el-table-column prop="status" label="状态" width="60">
                            <template slot-scope="scope">
                              <span class="status-running">运行中</span>
                            </template>
                          </el-table-column>
                        </el-table>
                      </div>
                    </div>
                  </div>

                  <!-- 底部统计信息 -->
                  <div class="api-statistics">
                    <span>共选择 {{ selectedRequests.length }} 接口</span>
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
        <div class="drawer-footer">
          <el-button @click="showStepDrawer = false">取消</el-button>
          <el-button @click="resetSelection">重置</el-button>
          <el-button type="primary" @click="importSelectedRequests"
            :disabled="selectedRequests.length === 0 && selectedCases.length === 0 && selectedScenes.length === 0">导入</el-button>
        </div>
      </div>

      <!-- 普通步骤编辑界面 -->
      <div v-else>
        <el-form :model="stepForm" :rules="stepFormRules" ref="stepForm" label-width="100px">
          <el-form-item label="步骤类型" prop="step_type">
            <el-select v-model="stepForm.step_type" placeholder="请选择步骤类型" @change="onStepTypeChange"
              :disabled="isEditMode">
              <el-option label="API步骤" value="api"></el-option>
              <el-option label="等待步骤" value="wait"></el-option>
              <el-option label="控制器" value="controller"></el-option>
              <el-option label="测试用例" value="case"></el-option>
              <el-option label="脚本操作" value="script"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="步骤名称" prop="step_name">
            <el-input v-model="stepForm.step_name" placeholder="请输入步骤名称"></el-input>
          </el-form-item>

          <!-- API步骤配置 -->
          <template v-if="stepForm.step_type === 'api'">
            <el-form-item label="HTTP方法" prop="method">
              <el-select v-model="stepForm.method" placeholder="请选择HTTP方法">
                <el-option label="GET" value="GET"></el-option>
                <el-option label="POST" value="POST"></el-option>
                <el-option label="PUT" value="PUT"></el-option>
                <el-option label="DELETE" value="DELETE"></el-option>
                <el-option label="PATCH" value="PATCH"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="请求URL" prop="url">
              <el-input v-model="stepForm.url" placeholder="请输入请求URL"></el-input>
            </el-form-item>
          </template>

          <!-- 等待步骤配置 -->
          <template v-if="stepForm.step_type === 'wait'">
            <el-form-item label="等待时间" prop="wait_time">
              <el-input-number v-model="stepForm.wait_time" :min="0" :max="300000" placeholder="毫秒"
                style="width: 100%"></el-input-number>
            </el-form-item>
          </template>

          <!-- 控制器步骤配置 -->
          <template v-if="stepForm.step_type === 'controller'">
            <el-form-item label="条件表达式" prop="condition">
              <el-input v-model="stepForm.condition" placeholder="例如: ${response.status}"></el-input>
            </el-form-item>
            <el-form-item label="操作符" prop="operator">
              <el-select v-model="stepForm.operator" placeholder="请选择操作符">
                <el-option label="等于" value="equals"></el-option>
                <el-option label="不等于" value="not_equals"></el-option>
                <el-option label="大于" value="greater_than"></el-option>
                <el-option label="小于" value="less_than"></el-option>
                <el-option label="包含" value="contains"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="期望值" prop="expected_value">
              <el-input v-model="stepForm.expected_value" placeholder="请输入期望值"></el-input>
            </el-form-item>
          </template>

          <el-form-item label="描述">
            <el-input type="textarea" v-model="stepForm.description" placeholder="请输入步骤描述" :rows="3"></el-input>
          </el-form-item>
        </el-form>

        <div class="drawer-footer">
          <el-button @click="showStepDrawer = false">取消</el-button>
          <el-button type="primary" @click="saveStep">添加</el-button>
        </div>
      </div>
    </Drawer>
  </div>
</template>

<script>
import draggable from 'vuedraggable'
import {
  getApiTestCasesApi,
  getApiTestCollectionTreeV2Api,
  batchCreateSceneStepsApi,
  handleApiError,
  formatApiResponse
} from '@/api/apiTestCase'

export default {
  name: 'SceneStepsManager',
  components: {
    draggable
  },
  props: {
    projectId: {
      type: Number,
      required: true
    },
    sceneId: {
      type: [Number, String],
      default: null
    },
    steps: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      localSteps: [],
      showStepDrawer: false,
      editingIndex: -1,

      stepForm: {
        step_type: '',
        step_name: '',
        method: 'GET',
        url: '',
        wait_time: 1000,
        condition: '',
        operator: 'equals',
        expected_value: '',
        description: '',
        is_enabled: true
      },

      stepFormRules: {
        step_type: [
          { required: true, message: '请选择步骤类型', trigger: 'change' }
        ],
        step_name: [
          { required: true, message: '请输入步骤名称', trigger: 'blur' }
        ]
      },

      currentStepCommand: '',
      activeImportTab: 'api',
      importFilters: {
        project: 'all',
        search: ''
      },
      selectedRequests: [],
      selectedCases: [],
      selectedScenes: [],
      selectedCategory: 'all',
      searchTimer: null,
      systemRequests: [
        {
          id: 100002,
          name: '客户管位存',
          method: 'POST',
          path: '/ygl-yxgl/customer/save',
          status: 'running'
        },
        {
          id: 100001,
          name: '1111',
          method: 'GET',
          path: '/',
          status: 'running'
        },
        {
          id: 5,
          name: 'Deletecontent.halo.run/v1a1/contents/halo-dev/posts/5',
          method: 'DELETE',
          path: '/apis/content.halo.run/v1a1/contents/halo-dev/posts/5',
          status: 'running'
        },
        {
          id: 4,
          name: 'DraftPost',
          method: 'POST',
          path: '/apis/api.console.halo.run/v1a1/posts',
          status: 'running'
        },
        {
          id: 3,
          name: 'fetchPostHeadContent',
          method: 'GET',
          path: '/apis/api.console.halo.run/v1a1/posts/post-1/head-content',
          status: 'running'
        },
        {
          id: 2,
          name: 'PublishPost',
          method: 'PUT',
          path: '/apis/api.console.halo.run/v1a1/posts/post-1/publish',
          status: 'running'
        },
        {
          id: 1,
          name: 'RecyclePost',
          method: 'PUT',
          path: '/apis/api.console.halo.run/v1a1/posts/post-1/recycle',
          status: 'running'
        }
      ],
      systemCases: [],
      systemScenes: [],

      // API树形结构相关
      apiTreeData: [],
      treeLoading: false,
      treeProps: {
        children: 'children',
        label: 'name'
      },
      currentCollectionId: null,
      currentCollectionName: '',
      apiList: []
    }
  },
  computed: {
    drawerTitle() {
      return '导入系统请求'
    },
    filteredRequests() {
      let requests = this.systemRequests;
      if (this.importFilters.search) {
        const search = this.importFilters.search.toLowerCase();
        requests = requests.filter(req =>
          req.name.toLowerCase().includes(search) ||
          req.path.toLowerCase().includes(search)
        );
      }
      return requests;
    },
    filteredCases() {
      let cases = this.systemCases;
      if (this.importFilters.search) {
        const search = this.importFilters.search.toLowerCase();
        cases = cases.filter(caseItem =>
          caseItem.name.toLowerCase().includes(search) ||
          (caseItem.url && caseItem.url.toLowerCase().includes(search))
        );
      }
      return cases;
    },
    filteredScenes() {
      let scenes = this.systemScenes;
      if (this.importFilters.search) {
        const search = this.importFilters.search.toLowerCase();
        scenes = scenes.filter(scene =>
          scene.name.toLowerCase().includes(search) ||
          (scene.description && scene.description.toLowerCase().includes(search))
        );
      }
      return scenes;
    },
    filteredApiList() {
      let apis = this.apiList;

      // 如果选择了特定集合，只显示该集合下的接口
      if (this.currentCollectionId && this.currentCollectionId !== 'all') {
        apis = apis.filter(api => api.collection === this.currentCollectionId);
      }

      // 搜索筛选
      if (this.importFilters.search) {
        const search = this.importFilters.search.toLowerCase();
        apis = apis.filter(api =>
          api.name.toLowerCase().includes(search) ||
          (api.url && api.url.toLowerCase().includes(search)) ||
          (api.path && api.path.toLowerCase().includes(search))
        );
      }

      return apis;
    }
  },
  watch: {
    steps: {
      handler(newSteps) {
        this.localSteps = [...newSteps]
      },
      immediate: true,
      deep: true
    },
    localSteps: {
      handler(newSteps) {
        this.$emit('steps-change', newSteps)
      },
      deep: true
    },
    'importFilters.search': {
      handler() {
        // 防抖搜索
        clearTimeout(this.searchTimer);
        this.searchTimer = setTimeout(() => {
          this.fetchSystemRequests();
        }, 500);
      }
    }
  },
  methods: {
    // 刷新步骤
    refreshSteps() {
      this.$emit('refresh-steps')
    },

    // 拖拽开始
    onDragStart() {
      // 拖拽开始处理
    },

    // 拖拽结束
    onDragEnd() {
      // 更新步骤顺序
      this.localSteps.forEach((step, index) => {
        step.step_order = index + 1
      })
    },

    // 步骤类型变更
    onStepTypeChange() {
      // 重置相关字段
      this.stepForm.method = 'GET'
      this.stepForm.url = ''
      this.stepForm.wait_time = 1000
      this.stepForm.condition = ''
      this.stepForm.operator = 'equals'
      this.stepForm.expected_value = ''
    },

    // 保存步骤（添加或更新）
    saveStep() {
      this.$refs.stepForm.validate((valid) => {
        if (valid) {
          // 添加新步骤
          const newStep = {
            ...this.stepForm,
            id: Date.now(), // 临时ID
            step_order: this.localSteps.length + 1
          }
          this.localSteps.push(newStep)
          this.$message.success('步骤添加成功')
          this.showStepDrawer = false
          this.resetStepForm()
        }
      })
    },

    // 删除步骤
    deleteStep(step, index) {
      this.$confirm(`确定要删除步骤 "${step.step_name}" 吗？`, '确认删除', {
        type: 'warning'
      }).then(() => {
        this.localSteps.splice(index, 1)
        this.$message.success('步骤删除成功')
      }).catch(() => { })
    },

    // 更新步骤
    updateStep(step) {
      // 步骤更新处理
      console.log('更新步骤:', step)
      // TODO: 实现步骤更新逻辑
    },

    // 步骤命令处理
    handleStepCommand(command) {
      const { action, step, index } = command
      switch (action) {
        case 'copy':
          this.copyStep(step, index)
          break
        case 'run':
          this.runStep(step, index)
          break
        case 'disable':
          step.is_enabled = !step.is_enabled
          this.updateStep(step)
          break
      }
    },

    // 导入选中的请求
    async importSelectedRequests() {
      const totalSelected = this.selectedRequests.length + this.selectedCases.length + this.selectedScenes.length;
      if (totalSelected === 0) {
        this.$message.warning('请选择要导入的内容');
        return;
      }

      try {
        const loading = this.$loading({
          lock: true,
          text: '正在导入请求...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });

        // 构建步骤数据
        const stepsData = [];
        let currentOrder = this.localSteps.length + 1;

        // 处理接口导入
        this.selectedRequests.forEach((request) => {
          stepsData.push({
            scene_id: this.sceneId,
            step_name: request.name,
            step_type: 'api',
            step_order: currentOrder++,
            is_enabled: true,
            config: {
              method: request.method,
              url: request.path,
              headers: request.headers || {},
              params: request.params || {},
              body: request.body || {},
              assertions: [],
              pre_script: '',
              post_script: ''
            },
            description: `导入的系统请求: ${request.name}`,
            source_type: 'import_request',
            source_id: request.id
          });
        });

        // 处理用例导入
        this.selectedCases.forEach((caseItem) => {
          stepsData.push({
            scene_id: this.sceneId,
            step_name: caseItem.name,
            step_type: 'case',
            step_order: currentOrder++,
            is_enabled: true,
            config: {
              method: caseItem.method,
              url: caseItem.url,
              headers: caseItem.headers || {},
              params: caseItem.query_params || {},
              body: caseItem.body_data || {},
              assertions: caseItem.test_assertions || [],
              pre_script: caseItem.pre_request_script || '',
              post_script: caseItem.post_request_script || ''
            },
            description: `导入的测试用例: ${caseItem.name}`,
            source_type: 'import_case',
            source_id: caseItem.id
          });
        });

        // 处理场景导入
        this.selectedScenes.forEach((scene) => {
          stepsData.push({
            scene_id: this.sceneId,
            step_name: `引用场景: ${scene.name}`,
            step_type: 'scene',
            step_order: currentOrder++,
            is_enabled: true,
            config: {
              referenced_scene_id: scene.id,
              inherit_variables: true,
              inherit_headers: true
            },
            description: `导入的测试场景: ${scene.name}`,
            source_type: 'import_scene',
            source_id: scene.id
          });
        });

        // 批量创建步骤
        const response = await batchCreateSceneStepsApi(this.projectId, this.sceneId, {
          steps: stepsData
        });

        const result = formatApiResponse(response);

        if (result.success) {
          // 更新本地步骤列表
          const newSteps = result.data.steps || stepsData.map((step, index) => ({
            ...step,
            id: Date.now() + index,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }));

          this.localSteps.push(...newSteps);

          const importSummary = [];
          if (this.selectedRequests.length > 0) {
            importSummary.push(`${this.selectedRequests.length} 个接口`);
          }
          if (this.selectedCases.length > 0) {
            importSummary.push(`${this.selectedCases.length} 个用例`);
          }
          if (this.selectedScenes.length > 0) {
            importSummary.push(`${this.selectedScenes.length} 个场景`);
          }

          this.$message.success(`成功导入 ${importSummary.join('、')}`);
          this.showStepDrawer = false;
          this.resetImportData();

          // 触发步骤变更事件
          this.$emit('steps-imported', newSteps);
        } else {
          throw new Error(result.message || '导入失败');
        }

        loading.close();
      } catch (error) {
        loading.close();
        console.error('导入请求失败:', error);
        this.$message.error(handleApiError(error));
      }
    },

    // 获取系统请求列表
    async fetchSystemRequests() {
      try {
        const response = await getApiTestCasesApi(this.projectId, {
          page: 1,
          page_size: 100,
          search: this.importFilters.search,
          category: this.selectedCategory === 'all' ? '' : this.selectedCategory
        });

        const result = formatApiResponse(response);

        if (result.success) {
          this.systemRequests = result.data.items || [];
          this.apiList = result.data.items || [];
        } else {
          throw new Error(result.message || '获取请求列表失败');
        }
      } catch (error) {
        console.error('获取系统请求失败:', error);
        this.$message.error(handleApiError(error));
      }
    },

    // 获取API集合树形结构
    async fetchApiTreeData() {
      this.treeLoading = true;
      try {
        const response = await getApiTestCollectionTreeV2Api(this.projectId);

        if (response.data && response.data.code === 0) {
          this.apiTreeData = response.data.result || [];
        } else if (response.data) {
          this.apiTreeData = response.data || [];
        } else {
          throw new Error('获取API树形结构失败');
        }
      } catch (error) {
        console.error('获取API树形结构失败:', error);
        this.$message.error('获取接口目录失败');
        this.apiTreeData = [];
      } finally {
        this.treeLoading = false;
      }
    },

    // 处理树节点点击
    handleTreeNodeClick(data) {
      if (data.is_folder) {
        // 点击文件夹，显示该文件夹下的所有接口
        this.currentCollectionId = data.id;
        this.currentCollectionName = data.name;
      } else {
        // 点击接口，可以预览或其他操作
        console.log('选择接口:', data);
      }
    },

    // 树节点筛选
    filterTreeNode(value, data) {
      if (!value) return true;
      return data.name.toLowerCase().indexOf(value.toLowerCase()) !== -1;
    },

    // 选择分类
    async selectCategory(category) {
      this.selectedCategory = category;
      await this.fetchSystemRequests();
    },

    // 刷新请求列表
    async refreshRequests() {
      await this.fetchSystemRequests();
      this.$message.success('请求列表已刷新');
    },

    // 获取系统用例列表
    async fetchSystemCases() {
      try {
        const response = await getApiTestCasesApi(this.projectId, {
          page: 1,
          page_size: 100,
          search: this.importFilters.search
        });

        const result = formatApiResponse(response);

        if (result.success) {
          this.systemCases = result.data.items || [];
        } else {
          throw new Error(result.message || '获取用例列表失败');
        }
      } catch (error) {
        console.error('获取系统用例失败:', error);
        this.$message.error(handleApiError(error));
      }
    },

    // 刷新用例列表
    async refreshCases() {
      await this.fetchSystemCases();
      this.$message.success('用例列表已刷新');
    },

    // 获取系统场景列表
    async fetchSystemScenes() {
      try {
        // 这里需要调用获取场景列表的API
        // const response = await getApiTestScenesApi(this.projectId, {
        //   page: 1,
        //   page_size: 100,
        //   search: this.importFilters.search
        // });

        // 临时使用模拟数据
        this.systemScenes = [
          {
            id: 1,
            name: '用户登录场景',
            status: 'completed',
            step_count: 5,
            description: '完整的用户登录流程测试'
          },
          {
            id: 2,
            name: '商品购买场景',
            status: 'progress',
            step_count: 8,
            description: '从浏览商品到完成购买的完整流程'
          }
        ];
      } catch (error) {
        console.error('获取系统场景失败:', error);
        this.$message.error('获取场景列表失败');
      }
    },

    // 刷新场景列表
    async refreshScenes() {
      await this.fetchSystemScenes();
      this.$message.success('场景列表已刷新');
    },

    // 刷新API数据
    async refreshApiData() {
      await Promise.all([
        this.fetchApiTreeData(),
        this.fetchSystemRequests()
      ]);
      this.$message.success('接口数据已刷新');
    },

    // 刷新API列表
    async refreshApiList() {
      await this.fetchSystemRequests();
    },

    // 全选API
    selectAllApis() {
      this.$refs.apiTable.toggleAllSelection();
    },

    // 清空API选择
    clearApiSelection() {
      this.$refs.apiTable.clearSelection();
      this.selectedRequests = [];
    },

    // 重置导入数据
    resetImportData() {
      this.selectedRequests = [];
      this.selectedCases = [];
      this.selectedScenes = [];
      this.importFilters = {
        project: 'all',
        search: ''
      };
      this.selectedCategory = 'all';
      this.currentCollectionId = null;
      this.currentCollectionName = '';
    },

    // 处理添加步骤命令
    async handleAddStepCommand(command) {
      this.currentStepCommand = command;

      if (command === 'import_request') {
        // 显示导入系统请求界面
        this.showStepDrawer = true;
        this.resetImportData();
        // 获取系统请求、用例、场景列表和API树形结构
        await Promise.all([
          this.fetchApiTreeData(),
          this.fetchSystemRequests(),
          this.fetchSystemCases(),
          this.fetchSystemScenes()
        ]);
      } else {
        // 处理其他步骤类型
        const stepTypeMap = {
          'custom_request': 'api',
          'loop_controller': 'controller',
          'condition_controller': 'controller',
          'if_controller': 'controller',
          'script_step': 'script',
          'wait_step': 'wait'
        };

        const stepType = stepTypeMap[command];
        if (stepType) {
          this.initStepForm(stepType, command);
          this.showStepDrawer = true;
        }
      }
    },

    // 重置步骤表单
    resetStepForm() {
      this.stepForm = {
        step_type: '',
        step_name: '',
        method: 'GET',
        url: '',
        wait_time: 1000,
        condition: '',
        operator: 'equals',
        expected_value: '',
        description: '',
        is_enabled: true
      }
      this.editingIndex = -1
      this.currentStepCommand = ''
      if (this.$refs.stepForm) {
        this.$refs.stepForm.resetFields()
      }
    },

    // 获取步骤类型标签类型
    getStepTypeTagType(stepType) {
      const typeMap = {
        'api': 'primary',
        'wait': 'warning',
        'controller': 'success',
        'case': 'info'
      }
      return typeMap[stepType] || 'default'
    },

    // 获取步骤类型文本
    getStepTypeText(stepType) {
      const textMap = {
        'api': '基础 API',
        'wait': '等待时间',
        'controller': '条件控制器',
        'case': '基础 CASE',
        'script': '脚本操作'
      }
      return textMap[stepType] || stepType
    },

    // 获取HTTP方法标签类型
    getMethodTagType(method) {
      const typeMap = {
        'GET': 'success',
        'POST': 'primary',
        'PUT': 'warning',
        'DELETE': 'danger',
        'PATCH': 'info'
      }
      return typeMap[method] || 'default'
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'running': '运行中',
        'success': '成功',
        'failed': '失败',
        'stopped': '已停止',
        'pending': '等待中'
      }
      return statusMap[status] || status
    },

    // 处理表格选择变更
    handleSelectionChange(selection) {
      this.selectedRequests = selection
    },

    // 处理用例选择变更
    handleCaseSelectionChange(selection) {
      this.selectedCases = selection
    },

    // 处理场景选择变更
    handleSceneSelectionChange(selection) {
      this.selectedScenes = selection
    },

    // 重置选择
    resetSelection() {
      this.selectedRequests = []
      this.selectedCases = []
      this.selectedScenes = []
      // 清空表格选择
      this.$nextTick(() => {
        if (this.$refs.requestTable) {
          this.$refs.requestTable.clearSelection()
        }
        if (this.$refs.apiTable) {
          this.$refs.apiTable.clearSelection()
        }
        if (this.$refs.caseTable) {
          this.$refs.caseTable.clearSelection()
        }
        if (this.$refs.sceneTable) {
          this.$refs.sceneTable.clearSelection()
        }
      })
    },

    // 初始化步骤表单
    initStepForm(stepType, command) {
      this.resetStepForm()
      this.stepForm.step_type = stepType

      // 根据命令设置默认值
      switch (command) {
        case 'custom_request':
          this.stepForm.step_name = '自定义请求'
          this.stepForm.method = 'GET'
          break
        case 'loop_controller':
          this.stepForm.step_name = '循环控制器'
          this.stepForm.condition = '${loop_count} < 10'
          this.stepForm.operator = 'less_than'
          this.stepForm.expected_value = '10'
          break
        case 'condition_controller':
          this.stepForm.step_name = '条件控制器'
          this.stepForm.condition = '${response.status}'
          this.stepForm.operator = 'equals'
          this.stepForm.expected_value = '200'
          break
        case 'if_controller':
          this.stepForm.step_name = '仅一次控制器'
          this.stepForm.condition = '${executed_once}'
          this.stepForm.operator = 'equals'
          this.stepForm.expected_value = 'false'
          break
        case 'script_step':
          this.stepForm.step_name = '脚本操作'
          this.stepForm.step_type = 'script'
          break
        case 'wait_step':
          this.stepForm.step_name = '等待时间'
          this.stepForm.wait_time = 1000
          break
      }
    },

    // 复制步骤
    copyStep(step) {
      const copiedStep = {
        ...step,
        id: Date.now(), // 新的临时ID
        step_name: `${step.step_name}_copy`,
        step_order: this.localSteps.length + 1
      }
      this.localSteps.push(copiedStep)
      this.$message.success('步骤复制成功')
    },

    // 单独执行步骤
    runStep(step) {
      this.$message.info(`正在执行步骤: ${step.step_name}`)
      // TODO: 实现单步执行逻辑
      console.log('执行步骤:', step)
    },

    // 获取场景状态标签类型
    getSceneStatusTagType(status) {
      const typeMap = {
        'completed': 'success',
        'progress': 'primary',
        'failed': 'danger',
        'pending': 'warning'
      }
      return typeMap[status] || 'default'
    },

    // 获取场景状态文本
    getSceneStatusText(status) {
      const statusMap = {
        'completed': '已完成',
        'progress': '进行中',
        'failed': '失败',
        'pending': '待执行'
      }
      return statusMap[status] || status
    }
  },

  // 组件销毁时清理定时器
  beforeDestroy() {
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }
  }
}
</script>

<style scoped>
.scene-steps-manager {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 步骤头部 */
.steps-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

.steps-count {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.steps-actions {
  display: flex;
  gap: 8px;
}

/* 步骤容器 */
.steps-container {
  flex: 1;
  overflow-y: auto;
}

/* 步骤项 */
.step-item {
  display: flex;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  background: #fff;
  transition: all 0.2s;
  gap: 12px;
}

.step-item:hover {
  border-color: #d9d9d9;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.step-item.disabled {
  opacity: 0.6;
  background: #f5f5f5;
}

.step-ghost {
  opacity: 0.5;
  background: #e6f7ff;
}

.step-chosen {
  border-color: #1890ff;
}

/* 步骤控制 */
.step-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 80px;
}

.step-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: #1890ff;
  color: white;
  border-radius: 50%;
  font-size: 12px;
  font-weight: 500;
}

.step-drag-handle {
  cursor: move;
  color: #999;
  padding: 4px;
}

.step-drag-handle:hover {
  color: #666;
}

/* 步骤内容 */
.step-content {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.step-type-badge {
  font-weight: 500;
}

.step-method-badge {
  font-weight: 500;
  font-size: 11px;
}

.step-description {
  font-size: 14px;
  color: #333;
}

.step-operator {
  font-size: 12px;
  color: #666;
  background: #f0f0f0;
  padding: 2px 6px;
  border-radius: 3px;
}

.step-value {
  font-size: 12px;
  color: #1890ff;
  background: #e6f7ff;
  padding: 2px 6px;
  border-radius: 3px;
}

.step-note {
  font-size: 12px;
  color: #999;
  margin-left: auto;
}

/* 步骤操作 */
.step-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.step-item:hover .step-actions {
  opacity: 1;
}

/* 添加步骤按钮 */
.add-step-dropdown-wrapper {
  width: 100%;
  display: block;
}

.add-step-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px;
  margin-top: 16px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  color: #666;
  transition: all 0.2s;
  width: 100%;
  box-sizing: border-box;
}

.add-step-button:hover {
  border-color: #1890ff;
  color: #1890ff;
  background: #f6ffed;
}

/* 添加步骤下拉菜单样式 */
.add-step-dropdown {
  min-width: 200px;
}

.add-step-dropdown .dropdown-header {
  color: #999;
  font-size: 12px;
  font-weight: 600;
  padding: 8px 16px 4px;
  cursor: default;
}

.add-step-dropdown .dropdown-header span {
  color: #666;
}

.add-step-dropdown .el-dropdown-menu__item {
  padding: 8px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.add-step-dropdown .el-dropdown-menu__item i {
  width: 16px;
  color: #666;
}

.feature-tag {
  margin-left: auto;
  font-size: 10px;
}

/* 抽屉样式 */
.drawer-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px 24px;
  border-top: 1px solid #e8e8e8;
  background: #fff;
  text-align: right;
}

/* 导入请求样式 */
.import-request-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.import-header {
  padding: 0 0 16px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

.import-header h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
}

.import-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.search-filters {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  gap: 16px;
}

.left-filters {
  display: flex;
  gap: 8px;
}

.right-filters {
  display: flex;
  gap: 8px;
  align-items: center;
}

.right-filters .el-input {
  width: 200px;
}

.request-list {
  flex: 1;
  overflow: hidden;
}

.request-path {
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 12px;
  color: #666;
}

.status-badge {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 3px;
}

.status-badge.running {
  background: #e6f7ff;
  color: #1890ff;
}

.import-statistics {
  padding: 12px 0;
  font-size: 12px;
  color: #666;
  border-top: 1px solid #f0f0f0;
}

/* 导入选项卡样式 */
.import-tabs .el-tabs__header {
  margin-bottom: 16px;
}

.import-tabs .el-tabs__nav-wrap {
  padding: 0 16px;
}

.import-tabs .el-tab-pane {
  padding: 0;
}

/* API导入布局样式 */
.api-import-layout {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.api-main-content {
  flex: 1;
  display: flex;
  gap: 12px;
  min-height: 400px;
}

/* 左侧API树形面板 */
.api-tree-panel {
  width: 200px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
}

.tree-header {
  padding: 8px 12px;
  background: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
  font-size: 13px;
  font-weight: 500;
  color: #606266;
}

.tree-content {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.custom-tree-node {
  display: flex;
  align-items: center;
  gap: 4px;
  width: 100%;
}

.tree-folder-icon {
  color: #409eff;
  font-size: 14px;
}

.tree-method-tag {
  font-size: 10px;
  padding: 1px 4px;
}

.tree-node-label {
  flex: 1;
  font-size: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.tree-node-count {
  font-size: 11px;
  color: #909399;
}

/* 右侧API列表面板 */
.api-list-panel {
  flex: 1;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
  font-size: 13px;
  font-weight: 500;
  color: #606266;
}

.list-actions {
  display: flex;
  gap: 4px;
}

.list-content {
  flex: 1;
  overflow: hidden;
}

.request-path {
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 11px;
  color: #666;
}

.status-running {
  color: #409eff;
  font-size: 11px;
}

/* API统计信息 */
.api-statistics {
  padding: 8px 0;
  font-size: 12px;
  color: #666;
  border-top: 1px solid #ebeef5;
  margin-top: 12px;
}

/* 表格样式优化 */
.request-list .el-table {
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.request-list .el-table th {
  background-color: #fafafa;
  font-weight: 600;
}

.request-list .el-table td {
  padding: 8px 0;
}

/* 统计信息样式 */
.import-statistics {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 4px;
  margin-top: 16px;
  font-size: 13px;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .step-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .step-controls {
    width: 100%;
    justify-content: space-between;
  }

  .step-content {
    width: 100%;
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .step-actions {
    opacity: 1;
    width: 100%;
    justify-content: flex-end;
  }

  .search-filters {
    flex-direction: column;
    gap: 12px;
  }

  .left-filters,
  .right-filters {
    width: 100%;
    justify-content: flex-start;
  }

  .right-filters .el-input {
    width: 100%;
  }
}
</style>
