<template>
  <div class="assertion-tab">
    <!-- 顶部添加断言下拉按钮 -->
    <div class="assertion-header">
      <el-dropdown @command="handleAddAssertion" trigger="click" placement="bottom-start">
        <el-button type="primary">
          <i class="el-icon-plus"></i>
          断言
          <i class="el-icon-arrow-down el-icon--right"></i>
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="status_code">状态码</el-dropdown-item>
          <el-dropdown-item command="response_header">响应头</el-dropdown-item>
          <el-dropdown-item command="response_body">响应体</el-dropdown-item>
          <el-dropdown-item command="response_time">响应时间</el-dropdown-item>
          <el-dropdown-item divided command="variable">变量</el-dropdown-item>
          <el-dropdown-item command="script">脚本</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>

    <!-- 断言内容区域 -->
    <div class="assertion-content">
      <!-- 左侧断言列表 -->
      <div class="assertion-list">
        <div v-for="(assertion, index) in assertions" :key="index" class="assertion-item"
          :class="{ 'active': activeIndex === index }" @click="selectAssertion(index)">
          <div class="assertion-item-header">
            <span class="assertion-number">{{ index + 1 }}</span>
            <span class="assertion-type">{{ getAssertionTypeText(assertion.type) }}</span>
            <div class="assertion-item-actions">
              <el-dropdown @command="(command) => handleAssertionCommand(command, index)" trigger="click">
                <el-button type="text" size="mini" icon="el-icon-more"></el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item :command="'copy'">复制</el-dropdown-item>
                  <el-dropdown-item :command="'delete'">删除</el-dropdown-item>
                  <el-dropdown-item :command="'toggle'">
                    {{ assertion.enabled ? '禁用' : '启用' }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>

          <div class="assertion-item-content">
            <!-- 状态码断言 -->
            <div v-if="assertion.type === 'status_code'" class="assertion-preview">
              <span class="preview-label">状态码</span>
              <span class="preview-operator">{{ getOperatorText(assertion.operator) }}</span>
              <span class="preview-value">{{ assertion.expected_value }}</span>
            </div>

            <!-- 响应头断言 -->
            <div v-else-if="assertion.type === 'response_header'" class="assertion-preview">
              <span class="preview-label">响应头</span>
              <span class="preview-field">{{ assertion.field }}</span>
              <span class="preview-operator">{{ getOperatorText(assertion.operator) }}</span>
              <span class="preview-value">{{ assertion.expected_value }}</span>
            </div>

            <!-- 响应体断言 -->
            <div v-else-if="assertion.type === 'response_body'" class="assertion-preview">
              <span class="preview-label">响应体</span>
              <span class="preview-operator">{{ getOperatorText(assertion.operator) }}</span>
              <span class="preview-value">{{ assertion.expected_value }}</span>
            </div>

            <!-- 响应时间断言 -->
            <div v-else-if="assertion.type === 'response_time'" class="assertion-preview">
              <span class="preview-label">响应时间</span>
              <span class="preview-operator">{{ getOperatorText(assertion.operator) }}</span>
              <span class="preview-value">{{ assertion.expected_value }}ms</span>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="assertions.length === 0" class="empty-state">
          <i class="el-icon-warning-outline"></i>
          <p>暂无断言，请点击上方按钮添加</p>
        </div>
      </div>

      <!-- 右侧断言配置面板 -->
      <div class="assertion-config-panel" v-if="activeIndex !== -1 && assertions[activeIndex]">
        <div class="config-panel-header">
          <h3>{{ getAssertionTypeText(currentAssertion.type) }}</h3>
        </div>

        <div class="config-form">
          <el-form :model="currentAssertion" label-width="80px">
            <!-- 断言类型 -->
            <el-form-item label="类型">
              <el-select v-model="currentAssertion.type" @change="onAssertionTypeChange" style="width: 100%">
                <el-option label="状态码" value="status_code"></el-option>
                <el-option label="响应头" value="response_header"></el-option>
                <el-option label="响应体" value="response_body"></el-option>
                <el-option label="响应时间" value="response_time"></el-option>
              </el-select>
            </el-form-item>

            <!-- 响应头字段名 -->
            <el-form-item label="字段名" v-if="currentAssertion.type === 'response_header'">
              <el-input v-model="currentAssertion.field" placeholder="如: Content-Type"></el-input>
            </el-form-item>

            <!-- 操作符 -->
            <el-form-item label="操作符">
              <el-select v-model="currentAssertion.operator" style="width: 100%">
                <el-option v-for="operator in getAvailableOperators()" :key="operator.value" :label="operator.label"
                  :value="operator.value"></el-option>
              </el-select>
            </el-form-item>

            <!-- 期望值 -->
            <el-form-item label="期望值">
              <el-input v-model="currentAssertion.expected_value" :type="getInputType()"
                :placeholder="getPlaceholder()"></el-input>
            </el-form-item>

            <!-- 描述 -->
            <el-form-item label="描述">
              <el-input v-model="currentAssertion.description" type="textarea" :rows="2"
                placeholder="断言描述（可选）"></el-input>
            </el-form-item>

            <!-- 启用状态 -->
            <el-form-item label="启用">
              <el-switch v-model="currentAssertion.enabled"></el-switch>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <!-- 右侧空状态 -->
      <div v-else class="config-panel-empty">
        <i class="el-icon-document-copy"></i>
        <p>请选择一个断言进行配置</p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AssertionTab',
  props: {
    value: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      assertions: [],
      activeIndex: -1,
      operatorOptions: {
        common: [
          { label: '等于', value: 'equals' },
          { label: '不等于', value: 'not_equals' },
          { label: '包含', value: 'contains' },
          { label: '不包含', value: 'not_contains' }
        ],
        numeric: [
          { label: '等于', value: 'equals' },
          { label: '不等于', value: 'not_equals' },
          { label: '大于', value: 'greater_than' },
          { label: '小于', value: 'less_than' },
          { label: '大于等于', value: 'greater_equal' },
          { label: '小于等于', value: 'less_equal' }
        ],
        string: [
          { label: '等于', value: 'equals' },
          { label: '不等于', value: 'not_equals' },
          { label: '包含', value: 'contains' },
          { label: '不包含', value: 'not_contains' },
          { label: '开始于', value: 'starts_with' },
          { label: '结束于', value: 'ends_with' },
          { label: '正则匹配', value: 'regex_match' }
        ]
      }
    }
  },
  computed: {
    currentAssertion() {
      return this.activeIndex !== -1 ? this.assertions[this.activeIndex] : null
    }
  },
  watch: {
    value: {
      handler(newVal) {
        this.assertions = newVal.length > 0 ? [...newVal] : []
      },
      immediate: true,
      deep: true
    },
    assertions: {
      handler(newVal) {
        this.$emit('input', newVal)
      },
      deep: true
    }
  },
  methods: {
    // 处理添加断言命令
    handleAddAssertion(type) {
      const assertionTemplates = {
        status_code: {
          type: 'status_code',
          operator: 'equals',
          expected_value: '200',
          enabled: true
        },
        response_header: {
          type: 'response_header',
          field: '',
          operator: 'equals',
          expected_value: '',
          enabled: true
        },
        response_body: {
          type: 'response_body',
          operator: 'contains',
          expected_value: '',
          enabled: true
        },
        response_time: {
          type: 'response_time',
          operator: 'less_than',
          expected_value: '1000',
          enabled: true
        },
        variable: {
          type: 'variable',
          operator: 'equals',
          expected_value: '',
          enabled: true
        },
        script: {
          type: 'script',
          operator: 'equals',
          expected_value: '',
          enabled: true
        }
      }

      const newAssertion = {
        ...assertionTemplates[type],
        description: ''
      }

      this.assertions.push(newAssertion)
      this.activeIndex = this.assertions.length - 1
    },

    // 选择断言
    selectAssertion(index) {
      this.activeIndex = index
    },

    // 处理断言操作命令
    handleAssertionCommand(command, index) {
      switch (command) {
        case 'copy':
          this.copyAssertion(index)
          break
        case 'delete':
          this.deleteAssertion(index)
          break
        case 'toggle':
          this.toggleAssertion(index)
          break
      }
    },

    // 删除断言
    deleteAssertion(index) {
      this.$confirm('确定要删除此断言吗？', '确认删除', {
        type: 'warning'
      }).then(() => {
        this.assertions.splice(index, 1)
        if (this.activeIndex === index) {
          this.activeIndex = this.assertions.length > 0 ? 0 : -1
        } else if (this.activeIndex > index) {
          this.activeIndex--
        }
        this.$message.success('断言删除成功')
      }).catch(() => { })
    },

    // 复制断言
    copyAssertion(index) {
      const originalAssertion = this.assertions[index]
      const copiedAssertion = {
        ...originalAssertion,
        description: originalAssertion.description + ' (副本)'
      }

      this.assertions.splice(index + 1, 0, copiedAssertion)
      this.activeIndex = index + 1
      this.$message.success('断言复制成功')
    },

    // 切换断言启用状态
    toggleAssertion(index) {
      this.assertions[index].enabled = !this.assertions[index].enabled
    },

    // 断言类型变更
    onAssertionTypeChange() {
      if (this.currentAssertion) {
        // 重置字段和期望值
        this.currentAssertion.field = ''
        this.currentAssertion.operator = this.getAvailableOperators()[0]?.value || 'equals'

        // 根据类型设置默认期望值
        switch (this.currentAssertion.type) {
          case 'status_code':
            this.currentAssertion.expected_value = '200'
            break
          case 'response_time':
            this.currentAssertion.expected_value = '1000'
            break
          default:
            this.currentAssertion.expected_value = ''
        }
      }
    },

    // 获取可用的操作符
    getAvailableOperators() {
      if (!this.currentAssertion) return []

      switch (this.currentAssertion.type) {
        case 'status_code':
        case 'response_time':
          return this.operatorOptions.numeric
        case 'response_header':
        case 'response_body':
          return this.operatorOptions.string
        default:
          return this.operatorOptions.common
      }
    },

    // 获取输入框类型
    getInputType() {
      if (!this.currentAssertion) return 'text'

      return ['status_code', 'response_time'].includes(this.currentAssertion.type)
        ? 'number'
        : 'text'
    },

    // 获取输入框占位符
    getPlaceholder() {
      if (!this.currentAssertion) return ''

      switch (this.currentAssertion.type) {
        case 'status_code':
          return '如: 200'
        case 'response_header':
          return '如: application/json'
        case 'response_body':
          return '如: success'
        case 'response_time':
          return '如: 1000'
        default:
          return '请输入期望值'
      }
    },

    // 获取断言类型文本
    getAssertionTypeText(type) {
      const typeMap = {
        'status_code': '状态码',
        'response_header': '响应头',
        'response_body': '响应体',
        'response_time': '响应时间'
      }
      return typeMap[type] || type
    },

    // 获取操作符文本
    getOperatorText(operator) {
      const allOperators = [
        ...this.operatorOptions.common,
        ...this.operatorOptions.numeric,
        ...this.operatorOptions.string
      ]

      const operatorObj = allOperators.find(op => op.value === operator)
      return operatorObj ? operatorObj.label : operator
    }
  }
}
</script>

<style scoped>
.assertion-tab {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 500px;
}

/* 断言头部 */
.assertion-header {
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

/* 断言内容区域 */
.assertion-content {
  display: flex;
  flex: 1;
  min-height: 0;
}

/* 左侧断言列表 */
.assertion-list {
  width: 300px;
  border-right: 1px solid #f0f0f0;
  overflow-y: auto;
  background: #fafafa;
}

.assertion-item {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.2s;
  background: white;
  margin: 8px;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
}

.assertion-item:hover {
  border-color: #d9d9d9;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.assertion-item.active {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

.assertion-item-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.assertion-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  background: #1890ff;
  color: white;
  border-radius: 50%;
  font-size: 12px;
  margin-right: 8px;
  font-weight: 500;
}

.assertion-type {
  font-weight: 500;
  flex: 1;
  font-size: 14px;
  color: #262626;
}

.assertion-item-actions {
  opacity: 0;
  transition: opacity 0.2s;
}

.assertion-item:hover .assertion-item-actions {
  opacity: 1;
}

.assertion-item-content {
  padding-left: 26px;
}

.assertion-preview {
  display: flex;
  align-items: center;
  gap: 6px;
  flex-wrap: wrap;
  font-size: 12px;
}

.preview-label {
  color: #666;
  font-weight: 500;
}

.preview-field {
  color: #1890ff;
  background: #e6f7ff;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: monospace;
}

.preview-operator {
  color: #666;
  background: #f0f0f0;
  padding: 2px 6px;
  border-radius: 3px;
}

.preview-value {
  color: #52c41a;
  background: #f6ffed;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: monospace;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #999;
  text-align: center;
}

.empty-state i {
  font-size: 48px;
  color: #ddd;
  margin-bottom: 16px;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}

/* 右侧配置面板 */
.assertion-config-panel {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background: white;
}

.config-panel-header {
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.config-panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.config-form {
  max-width: 400px;
}

/* 右侧空状态 */
.config-panel-empty {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  text-align: center;
}

.config-panel-empty i {
  font-size: 64px;
  color: #ddd;
  margin-bottom: 16px;
}

.config-panel-empty p {
  margin: 0;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .assertion-content {
    flex-direction: column;
  }

  .assertion-list {
    width: 100%;
    max-height: 300px;
    border-right: none;
    border-bottom: 1px solid #f0f0f0;
  }

  .assertion-config-panel {
    min-height: 300px;
  }
}
</style>
