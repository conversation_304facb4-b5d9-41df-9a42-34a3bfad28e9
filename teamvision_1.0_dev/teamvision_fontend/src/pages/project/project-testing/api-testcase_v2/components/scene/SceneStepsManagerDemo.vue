<template>
  <div class="scene-steps-manager-demo">
    <div class="demo-header">
      <h2>SceneStepsManager 组件演示</h2>
      <p>展示完善后的步骤管理功能，包括导入系统请求、用例和场景等功能</p>
    </div>

    <div class="demo-content">
      <el-card class="demo-card">
        <div slot="header" class="card-header">
          <span>步骤管理器</span>
          <div class="demo-controls">
            <el-button size="small" @click="addMockStep">添加模拟步骤</el-button>
            <el-button size="small" @click="clearSteps">清空步骤</el-button>
            <el-button size="small" type="primary" @click="showStats">显示统计</el-button>
          </div>
        </div>

        <SceneStepsManager :project-id="demoProjectId" :scene-id="demoSceneId" :steps="demoSteps"
          @steps-change="handleStepsChange" @refresh-steps="handleRefreshSteps" @steps-imported="handleStepsImported" />
      </el-card>

      <el-card class="demo-info">
        <div slot="header">
          <span>功能说明</span>
        </div>
        <div class="feature-list">
          <h4>✨ 主要功能</h4>
          <ul>
            <li><strong>拖拽排序</strong>：支持步骤的拖拽重新排序</li>
            <li><strong>多种步骤类型</strong>：API、等待、控制器、脚本、用例等</li>
            <li><strong>导入功能</strong>：从接口、用例、场景导入步骤</li>
            <li><strong>批量操作</strong>：支持批量选择和导入</li>
            <li><strong>实时搜索</strong>：支持关键词搜索和筛选</li>
          </ul>

          <h4>🔄 导入类型</h4>
          <ul>
            <li><strong>导入系统请求</strong>：左侧目录树 + 右侧接口列表布局</li>
            <li><strong>导入测试用例</strong>：从现有用例导入完整配置</li>
            <li><strong>导入测试场景</strong>：引用其他场景作为步骤</li>
          </ul>

          <h4>🆕 最新更新</h4>
          <ul>
            <li><strong>接口面板重构</strong>：严格按照UI设计实现左右分栏布局</li>
            <li><strong>目录树导航</strong>：左侧显示接口集合的层级结构</li>
            <li><strong>动态筛选</strong>：点击目录树节点筛选右侧接口列表</li>
            <li><strong>批量操作</strong>：支持全选、筛选、刷新等快捷操作</li>
          </ul>

          <h4>🎯 使用方法</h4>
          <ol>
            <li>点击"添加步骤"按钮</li>
            <li>选择"导入系统请求"</li>
            <li>在弹出的抽屉中选择要导入的内容</li>
            <li>支持在"接口"、"用例"、"场景"三个选项卡间切换</li>
            <li>使用搜索框筛选内容</li>
            <li>选择需要的项目后点击"导入"</li>
          </ol>
        </div>
      </el-card>
    </div>

    <!-- 统计信息对话框 -->
    <el-dialog title="步骤统计" :visible.sync="showStatsDialog" width="500px">
      <div class="stats-content">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="stat-item">
              <div class="stat-number">{{ demoSteps.length }}</div>
              <div class="stat-label">总步骤数</div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="stat-item">
              <div class="stat-number">{{ enabledStepsCount }}</div>
              <div class="stat-label">启用步骤</div>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20" style="margin-top: 20px;">
          <el-col :span="8" v-for="(count, type) in stepTypeStats" :key="type">
            <div class="stat-item">
              <div class="stat-number">{{ count }}</div>
              <div class="stat-label">{{ getStepTypeLabel(type) }}</div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import SceneStepsManager from './SceneStepsManager.vue'

export default {
  name: 'SceneStepsManagerDemo',
  components: {
    SceneStepsManager
  },
  data() {
    return {
      demoProjectId: 1,
      demoSceneId: 1,
      showStatsDialog: false,
      demoSteps: [
        {
          id: 1,
          step_name: '用户登录',
          step_type: 'api',
          method: 'POST',
          url: '/api/auth/login',
          is_enabled: true,
          step_order: 1,
          description: '用户登录接口'
        },
        {
          id: 2,
          step_name: '等待响应',
          step_type: 'wait',
          wait_time: 1000,
          is_enabled: true,
          step_order: 2,
          description: '等待1秒'
        },
        {
          id: 3,
          step_name: '检查登录状态',
          step_type: 'controller',
          condition: '${response.status}',
          operator: 'equals',
          expected_value: '200',
          is_enabled: true,
          step_order: 3,
          description: '验证登录是否成功'
        }
      ]
    }
  },
  computed: {
    enabledStepsCount() {
      return this.demoSteps.filter(step => step.is_enabled).length
    },
    stepTypeStats() {
      const stats = {}
      this.demoSteps.forEach(step => {
        stats[step.step_type] = (stats[step.step_type] || 0) + 1
      })
      return stats
    }
  },
  methods: {
    handleStepsChange(newSteps) {
      this.demoSteps = newSteps
      this.$message.success('步骤已更新')
    },
    handleRefreshSteps() {
      this.$message.info('刷新步骤列表')
    },
    handleStepsImported(newSteps) {
      this.$message.success(`成功导入 ${newSteps.length} 个步骤`)
    },
    addMockStep() {
      const newStep = {
        id: Date.now(),
        step_name: `模拟步骤 ${this.demoSteps.length + 1}`,
        step_type: 'api',
        method: 'GET',
        url: '/api/mock',
        is_enabled: true,
        step_order: this.demoSteps.length + 1,
        description: '这是一个模拟步骤'
      }
      this.demoSteps.push(newStep)
    },
    clearSteps() {
      this.$confirm('确定要清空所有步骤吗？', '确认操作', {
        type: 'warning'
      }).then(() => {
        this.demoSteps = []
        this.$message.success('步骤已清空')
      }).catch(() => { })
    },
    showStats() {
      this.showStatsDialog = true
    },
    getStepTypeLabel(type) {
      const labels = {
        'api': 'API步骤',
        'wait': '等待步骤',
        'controller': '控制器',
        'script': '脚本',
        'case': '用例'
      }
      return labels[type] || type
    }
  }
}
</script>

<style scoped>
.scene-steps-manager-demo {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.demo-header {
  text-align: center;
  margin-bottom: 30px;
}

.demo-header h2 {
  color: #333;
  margin-bottom: 10px;
}

.demo-header p {
  color: #666;
  font-size: 14px;
}

.demo-content {
  display: flex;
  gap: 20px;
}

.demo-card {
  flex: 2;
}

.demo-info {
  flex: 1;
  max-height: 600px;
  overflow-y: auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.demo-controls {
  display: flex;
  gap: 8px;
}

.feature-list h4 {
  color: #333;
  margin: 16px 0 8px 0;
}

.feature-list ul,
.feature-list ol {
  margin: 0 0 16px 0;
  padding-left: 20px;
}

.feature-list li {
  margin-bottom: 4px;
  line-height: 1.5;
}

.stats-content {
  text-align: center;
}

.stat-item {
  text-align: center;
  padding: 16px;
  border: 1px solid #eee;
  border-radius: 4px;
  background: #fafafa;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #1890ff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

@media (max-width: 768px) {
  .demo-content {
    flex-direction: column;
  }

  .demo-controls {
    flex-direction: column;
    gap: 4px;
  }
}
</style>
