<template>
  <div class="environments-panel">
    <div class="left-header">
      <div class="breadcrumb">
        <el-breadcrumb separator=">">
          <el-breadcrumb-item>环境变量</el-breadcrumb-item>
        </el-breadcrumb>
      </div>

      <div class="environment-header">
        <i class="el-icon-global globe-icon"></i>
        <span class="header-title">Global</span>
        <el-dropdown trigger="click" class="tree-actions">
          <span class="el-dropdown-link">
            <i class="el-icon-more"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>设置</el-dropdown-item>
            <el-dropdown-item>导出</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>

      <el-input v-model="searchQuery" placeholder="搜索" prefix-icon="el-icon-search" class="search-input" />

      <div class="action-buttons">
        <el-button type="primary" @click="showEnvironmentModal">
          <i class="el-icon-plus"></i> 新建
        </el-button>
        <div class="header-icons">
          <el-tooltip content="Folders" placement="bottom">
            <el-button icon="el-icon-folder" circle></el-button>
          </el-tooltip>
        </div>
      </div>
    </div>

    <div class="empty-state" v-if="environments.length === 0">
      <div class="empty-icon">
        <i class="el-icon-aim cube-icon"></i>
      </div>
      <div class="empty-title">环境变量为空</div>
      <div class="empty-subtitle">导入或创建环境变量</div>
      <div class="empty-actions">
        <el-button type="success" @click="importEnvironment">
          <i class="el-icon-upload2"></i> 导入
        </el-button>
        <el-button @click="showEnvironmentModal">
          <i class="el-icon-plus"></i> 新建
        </el-button>
      </div>
    </div>

    <div v-else class="environments-list">
      <el-card v-for="env in filteredEnvironments" :key="env.id" class="environment-item" shadow="hover"
        @click.native="selectEnvironment(env)">
        <div class="env-info">
          <div class="env-name">{{ env.name }}</div>
          <div class="env-description">{{ env.description || 'No description' }}</div>
        </div>
        <div class="env-actions">
          <el-tooltip content="编辑" placement="top">
            <el-button icon="el-icon-edit" circle @click.stop="editEnvironment(env)"></el-button>
          </el-tooltip>
          <el-tooltip content="删除" placement="top">
            <el-button icon="el-icon-delete" circle type="danger" @click.stop="deleteEnvironment(env.id)"></el-button>
          </el-tooltip>
        </div>
      </el-card>
    </div>

    <!-- 环境管理弹窗 -->
    <environment-modal :visible.sync="showEnvModal" :editData="currentEditEnvironment" @save="saveEnvironment"
      :projectID="projectID" />
  </div>
</template>

<script>
import EnvironmentModal from "./EnvironmentModal.vue";
import {
  getApiTestEnvironmentsApi,
  createApiTestEnvironmentApi,
  updateApiTestEnvironmentApi,
  deleteApiTestEnvironmentApi,
  handleApiError,
  formatApiResponse
} from '@/api/apiTestCase'
import { mapState } from 'vuex';

export default {
  name: 'EnvironmentsPanel',
  props: {
    projectID: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      loading: false,
      searchQuery: '',
      environments: [],
      selectedEnvironment: null,
      showEnvModal: false,
      currentEditEnvironment: null
    }
  },
  computed: {
    ...mapState('usercenter', ['userInfo']),

    filteredEnvironments() {
      if (!this.searchQuery) {
        return this.environments;
      }
      return this.environments.filter(env =>
        env.name.toLowerCase().includes(this.searchQuery.toLowerCase())
      );
    }
  },
  created() {
    // 确保 projectID 有效后再加载
    if (this.projectID && this.projectID > 0) {
      this.loadEnvironments()
    }
  },

  mounted() {
    // 如果 created 时 projectID 无效，在 mounted 时再次尝试
    if (!this.projectID || this.projectID <= 0) {
      this.$nextTick(() => {
        if (this.projectID && this.projectID > 0) {
          this.loadEnvironments()
        }
      })
    }
  },

  watch: {
    // 监听 projectID 变化
    projectID: {
      handler(newProjectID, oldProjectID) {
        console.log('EnvironmentsPanel projectID 变化:', oldProjectID, '->', newProjectID)
        if (newProjectID && newProjectID > 0 && newProjectID !== oldProjectID) {
          this.loadEnvironments()
        }
      },
      immediate: false
    }
  },
  methods: {
    // 加载环境列表
    async loadEnvironments() {
      this.loading = true
      try {
        const response = await getApiTestEnvironmentsApi(this.projectID)
        if (response.data.code) {
          this.environments = response.data.result || []
        } else {
          this.$message.error(response.data.message || '加载环境失败')
        }
      } catch (error) {
        this.$message.error(handleApiError(error))
      } finally {
        this.loading = false
      }
    },

    // 选择环境
    selectEnvironment(env) {
      this.selectedEnvironment = env
      this.$emit('environment-selected', env)
      this.$message.success(`已选择环境: ${env.name}`)
    },

    // 编辑环境
    editEnvironment(env) {
      this.showEnvironmentModal(env)
    },

    // 删除环境
    async deleteEnvironment(envId) {
      this.$confirm('确定要删除这个环境吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const response = await deleteApiTestEnvironmentApi(this.projectID, envId)
          if (response.data.code) {
            this.$message.success('删除成功')
            this.loadEnvironments()
          } else {
            this.$message.error(response.data.message || '删除失败')
          }
        } catch (error) {
          this.$message.error(handleApiError(error))
        }
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },

    // 导入环境
    importEnvironment() {
      const input = document.createElement('input')
      input.type = 'file'
      input.accept = '.json'
      input.onchange = (e) => {
        const file = e.target.files[0]
        if (file) {
          const reader = new FileReader()
          reader.onload = async (e) => {
            try {
              const data = JSON.parse(e.target.result)
              await this.importEnvironmentData(data)
            } catch (error) {
              this.$message.error('文件格式错误，请选择有效的JSON文件')
            }
          }
          reader.readAsText(file)
        }
      }
      input.click()
    },

    // 导入环境数据
    async importEnvironmentData(data) {
      try {
        // 假设导入的数据格式为 { name, description, variables, secrets }
        const environmentData = {
          name: data.name || 'Imported Environment',
          description: data.description || '',
          variables: data.variables || {},
          secrets: data.secrets || {},
          is_global: false
        }

        const response = await createApiTestEnvironmentApi(this.projectID, environmentData)
        if (response.data.code) {
          this.$message.success('环境导入成功！')
          this.loadEnvironments()
        } else {
          this.$message.error(response.data.message || '导入失败')
        }
      } catch (error) {
        this.$message.error(handleApiError(error))
      }
    },

    // 刷新环境列表
    refreshEnvironments() {
      this.loadEnvironments()
    },

    // 显示环境管理弹窗
    showEnvironmentModal(editData = null) {
      this.currentEditEnvironment = editData;
      this.showEnvModal = true;
    },
    // 隐藏环境管理弹窗
    hideEnvironmentModal() {
      this.showEnvModal = false;
      this.currentEditEnvironment = null;
    },
    // 保存环境
    async saveEnvironment(environmentData) {
      try {
        // 转换前端数据格式为后端API格式
        const apiData = {
          creator: this.userInfo.id,
          project_id: this.projectID,
          name: environmentData.label,
          description: environmentData.description || '',
          variables: {},
          secrets: {},
          is_global: false
        };

        // 转换variables数组为对象
        environmentData.variables.forEach(variable => {
          if (variable.key && variable.key.trim()) {
            apiData.variables[variable.key.trim()] = variable.value || '';
          }
        });

        // 转换secrets数组为对象
        environmentData.secrets.forEach(secret => {
          if (secret.key && secret.key.trim()) {
            apiData.secrets[secret.key.trim()] = secret.value || '';
          }
        });

        let response;
        let successMessage;

        if (environmentData.id) {
          // 编辑模式
          response = await updateApiTestEnvironmentApi(this.projectID, environmentData.id, apiData);
          successMessage = '环境更新成功！';
        } else {
          // 新建模式
          response = await createApiTestEnvironmentApi(this.projectID, apiData);
          successMessage = '环境创建成功！';
        }

        if (response.data.code) {
          this.$message.success(successMessage);
          this.hideEnvironmentModal();
          // 刷新环境列表数据
          this.refreshEnvironments();
        } else {
          this.$message.error(response.data.message || '保存环境失败');
        }
      } catch (error) {
        this.$message.error(handleApiError(error));
      }
    }
  },
  components: {
    EnvironmentModal
  }
}
</script>

<style scoped>
.environments-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.left-header {
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.breadcrumb {
  margin-bottom: 12px;
}

.environment-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.globe-icon {
  margin-right: 8px;
  font-size: 16px;
  color: #409eff;
}

.header-title {
  flex: 1;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.tree-actions {
  opacity: 0;
  transition: opacity 0.2s;
}

.environment-header:hover .tree-actions {
  opacity: 1;
}

.el-dropdown-link {
  cursor: pointer;
  color: #666;
  font-size: 12px;
}

.el-dropdown-link:hover {
  color: #409eff;
}

.search-input {
  margin-bottom: 12px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: space-between;
}

.header-icons {
  display: flex;
  gap: 4px;
  align-items: center;
}

.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.empty-icon {
  margin-bottom: 20px;
}

.cube-icon {
  font-size: 32px;
  opacity: 0.6;
  color: #c0c4cc;
}

.empty-title {
  font-size: 12px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.empty-subtitle {
  font-size: 8px;
  color: #666;
  margin-bottom: 24px;
}

.empty-actions {
  display: flex;
  gap: 12px;
}

.environments-list {
  flex: 1;
  padding: 8px;
  overflow-y: auto;
}

.environment-item {
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.environment-item:hover {
  transform: translateY(-2px);
}

.environment-item>>>.el-card__body {
  padding: 12px;
  display: flex;
  align-items: center;
}

.env-info {
  flex: 1;
}

.env-name {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.env-description {
  font-size: 12px;
  color: #666;
}

.env-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.environment-item:hover .env-actions {
  opacity: 1;
}
</style>
