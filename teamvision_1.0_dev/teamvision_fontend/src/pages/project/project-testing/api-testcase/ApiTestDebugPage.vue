<template>
  <div class="api-test-debug-page">
    <h2>API测试页面调试工具</h2>
    
    <div class="debug-section">
      <h3>当前状态</h3>
      <div class="status-grid">
        <div class="status-item">
          <label>Project ID:</label>
          <span>{{ projectID || '未设置' }}</span>
        </div>
        <div class="status-item">
          <label>路由参数:</label>
          <span>{{ JSON.stringify($route.params) }}</span>
        </div>
        <div class="status-item">
          <label>页面加载时间:</label>
          <span>{{ loadTime }}ms</span>
        </div>
      </div>
    </div>

    <div class="debug-section">
      <h3>组件初始化状态</h3>
      <div class="component-status">
        <div class="component-item">
          <label>ApiCaseContent:</label>
          <span :class="componentStatus.apiCaseContent">{{ componentStatus.apiCaseContent }}</span>
        </div>
        <div class="component-item">
          <label>CollectionsPanel:</label>
          <span :class="componentStatus.collectionsPanel">{{ componentStatus.collectionsPanel }}</span>
        </div>
        <div class="component-item">
          <label>EnvironmentsPanel:</label>
          <span :class="componentStatus.environmentsPanel">{{ componentStatus.environmentsPanel }}</span>
        </div>
      </div>
    </div>

    <div class="debug-section">
      <h3>控制台日志</h3>
      <div class="log-container">
        <div v-for="(log, index) in logs" :key="index" :class="['log-item', log.type]">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </div>

    <div class="debug-section">
      <h3>测试操作</h3>
      <div class="test-buttons">
        <el-button @click="testProjectIDChange" type="primary">
          模拟 ProjectID 变化
        </el-button>
        <el-button @click="clearLogs" type="info">
          清空日志
        </el-button>
        <el-button @click="refreshPage" type="warning">
          刷新页面
        </el-button>
        <el-button @click="checkApiStatus" type="success">
          检查API状态
        </el-button>
      </div>
    </div>

    <!-- 嵌入实际的API测试组件 -->
    <div class="debug-section">
      <h3>实际组件</h3>
      <div class="component-container">
        <ApiTestCase :project-i-d="projectID || 1" />
      </div>
    </div>
  </div>
</template>

<script>
import ApiTestCase from './ApiTestCase.vue'

export default {
  name: 'ApiTestDebugPage',
  components: {
    ApiTestCase
  },
  data() {
    return {
      projectID: null,
      loadTime: 0,
      startTime: Date.now(),
      componentStatus: {
        apiCaseContent: 'loading',
        collectionsPanel: 'loading',
        environmentsPanel: 'loading'
      },
      logs: [],
      originalConsole: {}
    }
  },
  created() {
    this.projectID = parseInt(this.$route.params.projectID) || 1
    this.setupConsoleInterception()
    this.addLog('info', `页面开始加载，ProjectID: ${this.projectID}`)
  },
  mounted() {
    this.loadTime = Date.now() - this.startTime
    this.addLog('success', `页面加载完成，耗时: ${this.loadTime}ms`)
    
    // 模拟组件状态检查
    setTimeout(() => {
      this.checkComponentStatus()
    }, 2000)
  },
  beforeDestroy() {
    this.restoreConsole()
  },
  methods: {
    setupConsoleInterception() {
      // 保存原始console方法
      this.originalConsole = {
        log: console.log,
        warn: console.warn,
        error: console.error
      }
      
      // 拦截console输出
      const self = this
      console.log = function(...args) {
        self.originalConsole.log.apply(console, args)
        if (args[0] && typeof args[0] === 'string' && args[0].includes('projectID')) {
          self.addLog('info', args.join(' '))
        }
      }
      
      console.warn = function(...args) {
        self.originalConsole.warn.apply(console, args)
        self.addLog('warning', args.join(' '))
      }
      
      console.error = function(...args) {
        self.originalConsole.error.apply(console, args)
        self.addLog('error', args.join(' '))
      }
    },
    
    restoreConsole() {
      console.log = this.originalConsole.log
      console.warn = this.originalConsole.warn
      console.error = this.originalConsole.error
    },
    
    addLog(type, message) {
      this.logs.unshift({
        type,
        message,
        time: new Date().toLocaleTimeString()
      })
      
      // 限制日志数量
      if (this.logs.length > 50) {
        this.logs = this.logs.slice(0, 50)
      }
    },
    
    checkComponentStatus() {
      // 模拟检查组件状态
      this.componentStatus.apiCaseContent = 'success'
      this.componentStatus.collectionsPanel = 'success'
      this.componentStatus.environmentsPanel = 'success'
      this.addLog('success', '所有组件初始化完成')
    },
    
    testProjectIDChange() {
      const newProjectID = this.projectID === 1 ? 2 : 1
      this.projectID = newProjectID
      this.addLog('info', `ProjectID 变更为: ${newProjectID}`)
    },
    
    clearLogs() {
      this.logs = []
      this.addLog('info', '日志已清空')
    },
    
    refreshPage() {
      window.location.reload()
    },
    
    async checkApiStatus() {
      this.addLog('info', '开始检查API状态...')
      try {
        // 这里可以添加实际的API状态检查
        this.addLog('success', 'API状态检查完成')
      } catch (error) {
        this.addLog('error', `API状态检查失败: ${error.message}`)
      }
    }
  }
}
</script>

<style scoped>
.api-test-debug-page {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.debug-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e0e6ed;
  border-radius: 8px;
  background: #f8f9fa;
}

.debug-section h3 {
  margin-top: 0;
  color: #333;
  border-bottom: 2px solid #1890ff;
  padding-bottom: 8px;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 15px;
}

.status-item, .component-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background: white;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.status-item label, .component-item label {
  font-weight: bold;
  color: #555;
}

.component-status {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.loading {
  color: #f39c12;
}

.success {
  color: #27ae60;
}

.error {
  color: #e74c3c;
}

.warning {
  color: #f39c12;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  background: #2c3e50;
  border-radius: 4px;
  padding: 10px;
}

.log-item {
  display: flex;
  margin-bottom: 5px;
  font-family: monospace;
  font-size: 12px;
}

.log-time {
  color: #95a5a6;
  margin-right: 10px;
  min-width: 80px;
}

.log-message {
  flex: 1;
}

.log-item.info .log-message {
  color: #3498db;
}

.log-item.success .log-message {
  color: #27ae60;
}

.log-item.warning .log-message {
  color: #f39c12;
}

.log-item.error .log-message {
  color: #e74c3c;
}

.test-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.component-container {
  border: 2px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
  min-height: 600px;
}
</style>
