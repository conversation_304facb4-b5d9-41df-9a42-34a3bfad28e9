<template>
  <div class="tab-test-page">
    <h2>API标签页功能测试</h2>
    
    <div class="test-section">
      <h3>测试说明</h3>
      <ul>
        <li>点击"新建请求标签页"（加号按钮）测试是否卡死</li>
        <li>快速连续点击测试防抖机制</li>
        <li>观察浏览器控制台的调试信息</li>
      </ul>
    </div>

    <div class="test-section">
      <h3>当前状态</h3>
      <p>活动标签页ID: {{ activeRequestId || '无' }}</p>
      <p>标签页数量: {{ requestTabs.length }}</p>
      <p>正在创建请求: {{ isCreatingNewRequest ? '是' : '否' }}</p>
      <p>标签页计数器: {{ tabCounter }}</p>
    </div>

    <div class="test-section">
      <h3>标签页列表</h3>
      <ul>
        <li v-for="tab in requestTabs" :key="tab.id">
          ID: {{ tab.id }}, 名称: {{ tab.name }}, 方法: {{ tab.method }}
        </li>
      </ul>
    </div>

    <div class="test-section">
      <h3>操作按钮</h3>
      <el-button @click="testCreateNewRequest" type="primary">
        手动创建新请求
      </el-button>
      <el-button @click="clearAllTabs" type="danger">
        清空所有标签页
      </el-button>
      <el-button @click="showDebugInfo" type="info">
        显示调试信息
      </el-button>
    </div>

    <!-- 嵌入实际的API测试组件 -->
    <div class="test-section">
      <h3>实际组件测试</h3>
      <div style="border: 1px solid #ddd; padding: 20px; border-radius: 8px;">
        <ApiCaseContent :project-i-d="1" />
      </div>
    </div>
  </div>
</template>

<script>
import ApiCaseContent from './ApiCaseContent.vue'

export default {
  name: 'TabTestPage',
  components: {
    ApiCaseContent
  },
  data() {
    return {
      // 模拟数据用于测试
      activeRequestId: null,
      requestTabs: [],
      tabCounter: 0,
      isCreatingNewRequest: false
    }
  },
  methods: {
    testCreateNewRequest() {
      console.log('测试创建新请求')
      this.tabCounter++
      const newId = 'test_request_' + this.tabCounter
      const newRequest = {
        id: newId,
        name: `Test Request ${this.tabCounter}`,
        method: 'GET',
        url: '',
        isNew: true
      }
      this.requestTabs.push(newRequest)
      this.activeRequestId = newId
      this.$message.success(`创建了新请求: ${newRequest.name}`)
    },
    
    clearAllTabs() {
      this.requestTabs = []
      this.activeRequestId = null
      this.tabCounter = 0
      this.isCreatingNewRequest = false
      this.$message.info('已清空所有标签页')
    },
    
    showDebugInfo() {
      const info = {
        activeRequestId: this.activeRequestId,
        requestTabsCount: this.requestTabs.length,
        tabCounter: this.tabCounter,
        isCreatingNewRequest: this.isCreatingNewRequest,
        requestTabs: this.requestTabs.map(tab => ({
          id: tab.id,
          name: tab.name,
          method: tab.method
        }))
      }
      console.log('调试信息:', info)
      this.$message.info('调试信息已输出到控制台')
    }
  }
}
</script>

<style scoped>
.tab-test-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e0e6ed;
  border-radius: 8px;
  background: #f8f9fa;
}

.test-section h3 {
  margin-top: 0;
  color: #333;
  border-bottom: 2px solid #1890ff;
  padding-bottom: 8px;
}

.test-section ul {
  margin: 10px 0;
  padding-left: 20px;
}

.test-section li {
  margin: 5px 0;
  color: #666;
}

.test-section p {
  margin: 8px 0;
  color: #555;
  font-family: monospace;
  background: white;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.el-button {
  margin-right: 10px;
  margin-bottom: 10px;
}
</style>
