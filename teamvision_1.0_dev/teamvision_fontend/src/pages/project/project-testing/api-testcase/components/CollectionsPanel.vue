<template>
  <div class="collections-panel">
    <div class="left-header">
      <div class="breadcrumb">
        <el-breadcrumb>
          <el-breadcrumb-item>API 用例集</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      <el-input v-model="searchQuery" placeholder="搜索" prefix-icon="el-icon-search" class="search-input" />
      <div class="action-buttons">
        <el-dropdown @command="handleNewCommand">
          <el-button type="primary">
            <i class="el-icon-plus"></i> 新建
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="folder">新建Suite</el-dropdown-item>
            <el-dropdown-item command="request">新建请求</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <div class="header-icons">
          <el-tooltip content="刷新" placement="bottom">
            <el-button icon="el-icon-refresh" circle @click="loadCollections" :loading="loading"></el-button>
          </el-tooltip>
          <el-tooltip content="下载" placement="bottom">
            <el-button icon="el-icon-download" circle @click="exportCollections"></el-button>
          </el-tooltip>
          <el-tooltip content="更多" placement="bottom">
            <el-button icon="el-icon-more" circle></el-button>
          </el-tooltip>
        </div>
      </div>
    </div>

    <div class="collection-tree" v-loading="loading">
      <el-tree ref="tree" :data="treeData" :props="treeProps" :filter-node-method="filterNode" :load="loadNode"
        node-key="id" :expand-on-click-node="false" :highlight-current="true" @node-click="handleNodeClick"
        :allow-drop="allowDrop" :allow-drag="allowDrag" lazy draggable>
        <span class="custom-tree-node" slot-scope="{ node, data }">
          <i v-if="data.is_folder" class="el-icon-folder"></i>
          <el-tag v-if="data.method" :type="getMethodTagType(data.method)" class="method-tag">{{ data.method }}</el-tag>
          <span class="tree-label">{{ node.label }}</span>
          <span class="case-count" v-if="data.is_folder && data.case_count > 0">({{ data.case_count }})</span>
          <el-dropdown trigger="click" @command="handleCommand" class="tree-actions">
            <span class="el-dropdown-link">
              <i class="el-icon-more"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-if="data.is_folder"
                :command="{ action: 'add-folder', data }">添加子文件夹</el-dropdown-item>
              <el-dropdown-item v-if="data.is_folder" :command="{ action: 'add-request', data }">添加请求</el-dropdown-item>
              <el-dropdown-item :command="{ action: 'edit', data }">编辑</el-dropdown-item>
              <el-dropdown-item v-if="!data.is_folder" :command="{ action: 'duplicate', data }">复制</el-dropdown-item>
              <el-dropdown-item :command="{ action: 'delete', data }" divided>删除</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </span>
      </el-tree>
    </div>

    <!-- 新建/编辑集合对话框 -->
    <el-dialog :title="collectionDialogTitle" :visible.sync="showCollectionDialog" width="500px"
      @close="resetCollectionForm">
      <el-form :model="collectionForm" :rules="collectionRules" ref="collectionForm" label-width="60px">
        <el-form-item label="名称" prop="name">
          <el-input v-model="collectionForm.name" placeholder="请输入集合名称"></el-input>
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="collectionForm.description" type="textarea" placeholder="请输入描述"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showCollectionDialog = false">取消</el-button>
        <el-button type="primary" @click="saveCollection" :loading="saving">保存</el-button>
      </div>
    </el-dialog>

    <!-- 新建/编辑请求对话框 -->
    <el-dialog :title="requestDialogTitle" :visible.sync="showRequestDialog" width="500px" @close="resetRequestForm">
      <el-form :model="requestForm" :rules="requestRules" ref="requestForm" label-width="60px">
        <el-form-item label="名称" prop="name">
          <el-input v-model="requestForm.name" placeholder="请输入请求名称"></el-input>
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="requestForm.description" type="textarea" placeholder="请输入描述"></el-input>
        </el-form-item>
        <el-form-item label="方法" prop="method">
          <el-select v-model="requestForm.method" placeholder="请选择HTTP方法">
            <el-option label="GET" value="GET"></el-option>
            <el-option label="POST" value="POST"></el-option>
            <el-option label="PUT" value="PUT"></el-option>
            <el-option label="DELETE" value="DELETE"></el-option>
            <el-option label="PATCH" value="PATCH"></el-option>
            <el-option label="HEAD" value="HEAD"></el-option>
            <el-option label="OPTIONS" value="OPTIONS"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="URL" prop="url">
          <el-input v-model="requestForm.url" placeholder="请输入请求URL"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showRequestDialog = false">取消</el-button>
        <el-button type="primary" @click="saveRequest" :loading="saving">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapState, mapMutations } from 'vuex'
import {
  getApiTestCollectionTreeApi,
  getApiTestCollectionTreeV2Api,
  createApiTestCollectionApi,
  updateApiTestCollectionApi,
  deleteApiTestCollectionApi,
  createApiTestCaseApi,
  handleApiError,
} from '@/api/apiTestCase'

export default {
  name: 'CollectionsPanel',
  props: {
    projectID: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      loading: false,
      saving: false,
      searchQuery: '',
      treeProps: {
        children: 'children',
        label: 'name'
      },
      treeData: [],

      // 集合对话框
      showCollectionDialog: false,
      collectionDialogMode: 'create', // create, edit
      collectionForm: {
        project_id: 0,
        creator: 0,
        name: '',
        description: '',
        parent: 0
      },
      collectionRules: {
        name: [
          { required: true, message: '请输入集合名称', trigger: 'blur' }
        ]
      },

      // 请求对话框
      showRequestDialog: false,
      requestDialogMode: 'create', // create, edit
      requestForm: {
        project_id: 0,
        creator: 0,
        name: '',
        description: '',
        collection: 0,
        method: 'GET',
        url: ''
      },
      requestRules: {
        name: [
          { required: true, message: '请输入请求名称', trigger: 'blur' }
        ],
        method: [
          { required: true, message: '请选择HTTP方法', trigger: 'change' }
        ],
        url: [
          { required: true, message: '请输入请求URL', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    ...mapState('usercenter', ['userInfo']),

    collectionDialogTitle() {
      return this.collectionDialogMode === 'edit' ? '编辑文件夹' : '新建文件夹'
    },
    requestDialogTitle() {
      return this.requestDialogMode === 'edit' ? '编辑请求' : '新建请求'
    }
  },
  watch: {
    searchQuery(val) {
      this.$refs.tree && this.$refs.tree.filter(val);
    },

    // 监听 projectID 变化
    projectID: {
      handler(newProjectID, oldProjectID) {
        console.log('CollectionsPanel projectID 变化:', oldProjectID, '->', newProjectID)
        if (newProjectID && newProjectID > 0 && newProjectID !== oldProjectID) {
          this.loadCollections()
        }
      },
      immediate: false
    }
  },
  created() {
    // 确保 projectID 有效后再加载
    if (this.projectID && this.projectID > 0) {
      this.loadCollections()
    }
  },

  mounted() {
    // 如果 created 时 projectID 无效，在 mounted 时再次尝试
    if (!this.projectID || this.projectID <= 0) {
      this.$nextTick(() => {
        if (this.projectID && this.projectID > 0) {
          this.loadCollections()
        }
      })
    }
  },
  methods: {
    ...mapMutations('apitestcase', ['setSelectApiCase']),

    // 懒加载节点数据
    async loadNode(node, resolve) {
      try {
        if (node.level === 0) {
          // 根节点，加载顶级集合
          const response = await getApiTestCollectionTreeV2Api(this.projectID, 0)
          if (response.data.code) {
            const rootNodes = response.data.result || []
            resolve(rootNodes)
          } else {
            resolve([])
          }
        } else {
          // 子节点，加载指定父级下的集合
          const parentId = node.data.id
          const response = await getApiTestCollectionTreeV2Api(this.projectID, parentId)
          if (response.data.code) {
            const childNodes = response.data.result || []
            resolve(childNodes)
          } else {
            resolve([])
          }
        }
      } catch (error) {
        console.error('懒加载节点失败:', error)
        // resolve([])
      }
    },

    // 加载集合树（保留用于刷新）
    async loadCollections() {
      // 验证 projectID
      if (!this.projectID || this.projectID <= 0) {
        console.warn('CollectionsPanel: 无效的 projectID，跳过集合加载:', this.projectID)
        return
      }

      this.loading = true
      try {
        console.log('CollectionsPanel: 刷新集合树，projectID:', this.projectID)
        // 重新加载根节点
        this.treeData = []
        this.$refs.tree && this.$refs.tree.setData([])

        // 触发根节点重新加载
        this.$nextTick(() => {
          if (this.$refs.tree) {
            this.$refs.tree.load()
          }
        })
      } catch (error) {
        console.error('CollectionsPanel: 刷新集合树失败:', error)
        this.$message.error(handleApiError(error))
      } finally {
        this.loading = false
      }
    },

    // 处理节点点击
    handleNodeClick(data) {
      console.log('选择节点:', data);
      if (!data.is_folder) {
        // 如果是请求节点，触发选择事件
        this.$emit('select-request', data)
      }
      this.setSelectApiCase(data)
    },

    // 处理新建命令
    handleNewCommand(command) {
      if (command === 'folder') {
        this.collectionForm = {
          project_id: this.projectID,
          creator: this.userInfo.id,
          name: '',
          description: '',
          parent: 0
        }
        this.collectionDialogMode = 'create'
        this.showCollectionDialog = true
      } else {
        this.requestForm = {
          project_id: this.projectID,
          creator: this.userInfo.id,
          name: '',
          description: '',
          collection: 0,
          method: 'GET',
          url: ''
        }
        this.requestDialogMode = 'create'
        this.showRequestDialog = true
      }
    },

    // 处理右键菜单命令
    async handleCommand({ action, data }) {
      switch (action) {
        case 'add-folder':
          this.addChildFolder(data)
          break
        case 'add-request':
          this.addChildRequest(data)
          break
        case 'edit':
          this.editItem(data)
          break
        case 'duplicate':
          this.duplicateItem(data)
          break
        case 'delete':
          this.deleteItem(data)
          break
      }
    },

    // 添加子文件夹
    addChildFolder(parent) {
      this.collectionForm = {
        project_id: this.projectID,
        creator: this.userInfo.id,
        name: '',
        description: '',
        parent: parent.id
      }
      this.collectionDialogMode = 'create'
      this.showCollectionDialog = true
    },

    // 添加子请求
    addChildRequest(parent) {
      this.requestForm = {
        project_id: this.projectID,
        creator: this.userInfo.id,
        name: '',
        description: '',
        collection: parent.id,
        method: 'GET',
        url: ''
      }
      this.requestDialogMode = 'create'
      this.showRequestDialog = true
    },

    // 编辑项目
    editItem(item) {
      if (item.is_folder) {
        this.collectionForm = {
          id: item.id,
          project_id: this.projectID,
          name: item.name,
          description: item.description || '',
          parent: item.parent || 0
        }
        this.collectionDialogMode = 'edit'
        this.showCollectionDialog = true
      } else {
        this.requestForm = {
          id: item.id,
          project_id: this.projectID,
          name: item.name,
          description: item.description || '',
          collection: item.collection || 0,
          method: item.method || 'GET',
          url: item.url || ''
        }
        this.requestDialogMode = 'edit'
        this.showRequestDialog = true
      }
    },

    // 复制项目
    async duplicateItem(item) {
      const newName = `${item.name} Copy`
      const duplicateData = {
        name: newName,
        description: item.description,
        is_folder: item.is_folder,
        parent: item.parent || 0
      }

      if (!item.is_folder) {
        duplicateData.method = item.method
        duplicateData.url = item.url
        duplicateData.headers = item.headers
        duplicateData.body_data = item.body_data
      }

      try {
        const response = item.is_folder
          ? await createApiTestCollectionApi(this.projectID, duplicateData)
          : await createApiTestCaseApi(this.projectID, duplicateData)

        if (response.data.code) {
          this.$message.success('复制成功')
          this.loadCollections()
        } else {
          this.$message.error(response.data.message || '复制失败')
        }
      } catch (error) {
        this.$message.error(handleApiError(error))
      }
    },

    // 删除项目
    deleteItem(item) {
      this.$confirm(`确定要删除 "${item.name}" 吗？`, '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const response = await deleteApiTestCollectionApi(this.projectID, item.id)

          if (response.data.code) {
            this.$message.success('删除成功')
            this.loadCollections()
          } else {
            this.$message.error(response.data.message || '删除失败')
          }
        } catch (error) {
          this.$message.error(handleApiError(error))
        }
      })
    },

    // 保存集合
    async saveCollection() {
      this.$refs.collectionForm.validate(async (valid) => {
        if (!valid) return

        this.saving = true
        try {
          let response
          if (this.collectionDialogMode === 'create') {
            response = await createApiTestCollectionApi(this.projectID, this.collectionForm)
          } else {
            response = await updateApiTestCollectionApi(this.projectID, this.collectionForm.id, this.collectionForm)
          }

          if (response.data.code) {
            this.$message.success(this.collectionDialogMode === 'create' ? '创建成功' : '更新成功')
            this.showCollectionDialog = false
            this.loadCollections()
          } else {
            this.$message.error(response.data.message || '操作失败')
          }
        } catch (error) {
          this.$message.error(handleApiError(error))
        } finally {
          this.saving = false
        }
      })
    },

    // 保存请求
    async saveRequest() {
      this.$refs.requestForm.validate(async (valid) => {
        if (!valid) return

        this.saving = true
        try {
          let response
          if (this.requestDialogMode === 'create') {
            response = await createApiTestCaseApi(this.projectID, this.requestForm)
          } else {
            // 假设有更新请求的API
            response = await updateApiTestCollectionApi(this.projectID, this.requestForm.id, this.requestForm)
          }

          if (response.data.code) {
            this.$message.success(this.requestDialogMode === 'create' ? '创建成功' : '更新成功')
            this.showRequestDialog = false
            this.loadCollections()
          } else {
            this.$message.error(response.data.message || '操作失败')
          }
        } catch (error) {
          this.$message.error(handleApiError(error))
        } finally {
          this.saving = false
        }
      })
    },

    // 重置集合表单
    resetCollectionForm() {
      this.$refs.collectionForm && this.$refs.collectionForm.resetFields()
      this.collectionForm = {
        project_id: this.projectID,
        creator: this.userInfo.id,
        name: '',
        description: '',
        parent: 0
      }
    },

    // 重置请求表单
    resetRequestForm() {
      this.$refs.requestForm && this.$refs.requestForm.resetFields()
      this.requestForm = {
        project_id: this.projectID,
        creator: this.userInfo.id,
        name: '',
        description: '',
        collection: 0,
        method: 'GET',
        url: ''
      }
    },

    // 导出集合
    exportCollections() {
      this.$message.info('导出功能待实现')
    },

    // 过滤节点
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },

    // 获取方法标签类型
    getMethodTagType(method) {
      const methodTypes = {
        'GET': 'success',
        'POST': 'primary',
        'PUT': 'warning',
        'DELETE': 'danger',
        'PATCH': 'info',
        'HEAD': 'info',
        'OPTIONS': 'info'
      };
      return methodTypes[method] || 'info';
    },

    // 允许拖拽
    allowDrag(draggingNode) {
      return true
    },

    // 允许放置
    allowDrop(draggingNode, dropNode, type) {
      return type !== 'inner' || dropNode.data.is_folder
    }
  }
}
</script>

<style scoped>
.collections-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.left-header {
  padding: 12px;
  border-bottom: 1px solid #e0e0e0;
}

.breadcrumb {
  margin-bottom: 8px;
}

.search-input {
  margin-bottom: 8px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: space-between;
}

.header-icons {
  display: flex;
  gap: 4px;
  align-items: center;
}

.collection-tree {
  flex: 1;
  padding: 8px;
  overflow-y: auto;
}

.custom-tree-node {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 4px 0;
}

.method-tag {}

.tree-label {
  flex: 1;
  font-size: 14px;
  color: #333;
  margin-left: 8px;
  margin-right: 8px;
}

.status-icon {
  margin-right: 8px;
  font-size: 12px;
}

.status-icon.success {
  color: #67c23a;
}

.status-icon.error {
  color: #f56c6c;
}

.case-count {
  margin-right: 4px;
}

.tree-actions {
  margin-right: 8px;
  opacity: 0;
  transition: opacity 0.2s;
}

.custom-tree-node:hover .tree-actions {
  opacity: 1;
}

.el-dropdown-link {
  cursor: pointer;
  color: #666;
  font-size: 12px;
}

.el-dropdown-link:hover {
  color: #409eff;
}
</style>
