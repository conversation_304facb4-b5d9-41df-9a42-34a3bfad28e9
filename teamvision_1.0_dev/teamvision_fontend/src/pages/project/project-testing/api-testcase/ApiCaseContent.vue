<template>
  <div class="api-case-content">
    <!-- 请求标签页栏 -->
    <div class="tabs-container">
      <el-tabs v-model="activeRequestId" type="card" @tab-remove="closeRequestTab" @tab-click="handleTabClick">
        <el-tab-pane v-for="request in requestTabs" :key="request.id" :label="request.name" :name="request.id"
          :closable="true">
          <span slot="label">
            <el-tag :type="getMethodTagType(request.method)" class="method-tag">{{ request.method }}</el-tag>
            {{ request.name }}
          </span>

          <!-- 每个标签页的完整内容 -->
          <div class="tab-pane-content">
            <!-- 请求构建区域 -->
            <div class="request-builder">
              <div class="request-header">
                <el-select v-model="currentRequestData.method" class="method-select" @change="updateRequest">
                  <el-option label="GET" value="GET"></el-option>
                  <el-option label="POST" value="POST"></el-option>
                  <el-option label="PUT" value="PUT"></el-option>
                  <el-option label="DELETE" value="DELETE"></el-option>
                  <el-option label="PATCH" value="PATCH"></el-option>
                </el-select>

                <el-input v-model="currentRequestData.url" placeholder="请输入请求URL" class="url-input"
                  @input="updateRequest" />

                <div class="action-buttons-right">
                  <el-dropdown @command="handleSendCommand">
                    <el-button type="primary" :loading="isLoading">
                      <i class="el-icon-right"></i> 发送
                      <i class="el-icon-arrow-down el-icon--right"></i>
                    </el-button>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item command="send">发送请求</el-dropdown-item>
                      <el-dropdown-item command="send-and-save">发送并保存</el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>

                  <el-dropdown @command="handleSaveCommand">
                    <el-button>
                      <i class="el-icon-document"></i> 保存
                      <i class="el-icon-arrow-down el-icon--right"></i>
                    </el-button>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item command="save">保存请求</el-dropdown-item>
                      <el-dropdown-item command="save-as">另存为 ...</el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </div>
              </div>
            </div>

            <!-- 请求内容区域 -->
            <div class="request-content-area">
              <!-- 选项卡 -->
              <el-tabs v-model="activeTab" class="content-tabs">
                <el-tab-pane label="参数" name="parameters">
                  <parameters-tab v-model="currentRequestData.query_params" @input="updateRequest" />
                </el-tab-pane>

                <el-tab-pane label="请求头" name="headers">
                  <headers-tab v-model="currentRequestData.headers" @input="updateRequest" />
                </el-tab-pane>

                <el-tab-pane label="请求体" name="body">
                  <body-tab v-model="currentRequestData.body_data" @input="updateRequest" />
                </el-tab-pane>

                <el-tab-pane label="认证" name="authorization">
                  <authorization-tab v-model="currentRequestData.auth_config" @input="updateRequest" />
                </el-tab-pane>

                <el-tab-pane label="前置脚本" name="pre-script">
                  <pre-script-tab v-model="currentRequestData.pre_request_script" @input="updateRequest" />
                </el-tab-pane>

                <el-tab-pane label="后置脚本" name="post-script">
                  <post-script-tab v-model="currentRequestData.post_request_script" @input="updateRequest" />
                </el-tab-pane>

                <el-tab-pane label="断言" name="assertion">
                  <assertion-tab v-model="currentRequestData.test_assertions" @input="updateRequest" />
                </el-tab-pane>
              </el-tabs>
            </div>

            <!-- 响应区域 -->
            <response-area v-if="responseData" :response="responseData" />
          </div>
        </el-tab-pane>

        <!-- 新建请求标签页 -->
        <el-tab-pane name="add-new" :closable="false">
          <span slot="label" class="add-new-tab">
            <i class="el-icon-plus"></i>
          </span>
        </el-tab-pane>
      </el-tabs>

      <!-- 环境选择器 - 悬浮组件 -->
      <div class="environment-selector-float">
        <el-dropdown @command="handleEnvironmentCommand" trigger="click">
          <span class="env-selector">
            <i class="el-icon-document"></i>
            <span>{{ selectedEnvironment || '选择环境变量' }}</span>
            <i class="el-icon-arrow-down"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="select">选择环境</el-dropdown-item>
            <el-dropdown-item command="manage">管理环境</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-tooltip content="View environment details" placement="bottom">
          <el-button icon="el-icon-view" circle class="env-view-btn" @click.stop></el-button>
        </el-tooltip>
      </div>
    </div>

    <!-- 另存为对话框 -->
    <el-dialog title="另存为" :visible.sync="saveAsDialogVisible" width="500px" :close-on-click-modal="false"
      class="save-as-dialog">
      <div class="save-as-content">
        <div class="form-group">
          <label class="form-label">请求名称</label>
          <el-input v-model="saveAsForm.name" placeholder="test" class="form-input">
          </el-input>
        </div>

        <div class="form-group">
          <label class="form-label">保存位置</label>
          <div class="collection-list" v-loading="collectionsLoading">
            <div class="new-collection-item" @click="showNewCollectionInput = !showNewCollectionInput">
              <i class="el-icon-plus"></i>
              <span>新建集合</span>
              <el-tooltip content="创建新集合" placement="right">
                <i class="el-icon-question help-icon"></i>
              </el-tooltip>
            </div>

            <div v-if="showNewCollectionInput" class="new-collection-input">
              <el-input v-model="newCollectionName" placeholder="请输入集合名称" size="small" :loading="creatingCollection"
                @keyup.enter.native="createNewCollection">
                <el-button slot="append" type="primary" size="small" :loading="creatingCollection"
                  @click="createNewCollection"> 创建 </el-button>
              </el-input>
            </div>

            <div v-for="collection in collections" :key="collection.id" class="collection-item"
              @click="selectCollection(collection.id)" :class="{
                active: saveAsForm.selectedCollection === collection.id,
                selected: saveAsForm.selectedCollection === collection.id
              }">
              <i v-if="saveAsForm.selectedCollection === collection.id" class="el-icon-success collection-check"></i>
              <i class="el-icon-folder"></i>
              <span>{{ collection.name }}</span>
              <el-button type="text" size="mini" icon="el-icon-more" class="more-btn"></el-button>
            </div>

            <div v-if="collections.length === 0 && !collectionsLoading" class="empty-collections">
              <i class="el-icon-folder-opened"></i>
              <span>暂无集合，请先创建一个集合</span>
            </div>
          </div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="saveAsDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmSaveAs" :loading="isSaving">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapState, mapMutations } from 'vuex';
import ParametersTab from './components/tabs/ParametersTab.vue';
import BodyTab from './components/tabs/BodyTab.vue';
import HeadersTab from './components/tabs/HeadersTab.vue';
import AuthorizationTab from './components/tabs/AuthorizationTab.vue';
import PreScriptTab from './components/tabs/PreScriptTab.vue';
import PostScriptTab from './components/tabs/PostScriptTab.vue';
import AssertionTab from './components/tabs/AssertionTab.vue';
import ResponseArea from './components/ResponseArea.vue';
import {
  createApiTestCaseApi,
  updateApiTestCaseApi,
  executeApiTestCaseApi,
  getApiTestEnvironmentsApi,
  handleApiError,
  getApiTestCaseDetailApi,
  getApiTestCollectionsApi,
  createApiTestCollectionApi
} from '@/api/apiTestCase';

export default {
  name: 'ApiCaseContent',
  props: {
    projectID: {
      type: Number,
      required: true
    },
    currentRequest: {
      type: Object,
      default: () => ({
        method: 'GET',
        url: 'https://www.baidu.com/',
        name: 'New Request'
      })
    }
  },
  data() {
    return {
      activeRequestId: null,
      activeTab: 'parameters',
      isLoading: false,
      isSaving: false,
      selectedEnvironment: null,
      selectedEnvironmentId: null,
      environments: [],
      requestTabs: [],
      currentRequestData: {
        id: null,
        collection: null,
        method: 'GET',
        url: '',
        name: 'New Request',
        description: '',
        headers: {},
        query_params: {},
        path_variables: {},
        body_type: 'none',
        body_data: {},
        auth_type: 'none',
        auth_config: {},
        pre_request_script: '',
        post_request_script: '',
        test_assertions: []
      },
      responseData: null,
      tabCounter: 0,
      saveAsDialogVisible: false,
      isCreatingNewRequest: false, // 防止重复创建请求
      showNewCollectionInput: false,
      newCollectionName: '',
      collectionsLoading: false,
      creatingCollection: false,
      saveAsForm: {
        name: 'test',
        selectedCollection: null
      },
      collections: []
    }
  },
  computed: {
    ...mapState('apitestcase', ['selectApiCase'])
  },
  created() {
    // 确保 projectID 有效后再初始化
    if (this.projectID && this.projectID > 0) {
      this.initializeComponent()
    }
  },

  mounted() {
    // 如果 created 时 projectID 无效，在 mounted 时再次尝试
    if (!this.projectID || this.projectID <= 0) {
      this.$nextTick(() => {
        if (this.projectID && this.projectID > 0) {
          this.initializeComponent()
        }
      })
    }
  },
  methods: {
    // 初始化组件
    async initializeComponent() {
      try {
        console.log('初始化 ApiCaseContent，projectID:', this.projectID)

        // 加载环境列表
        await this.loadEnvironments()

        // 延迟创建初始请求，避免与用户操作冲突
        this.$nextTick(() => {
          if (this.requestTabs.length === 0) {
            this.createNewRequest()
          }
        })
      } catch (error) {
        console.error('初始化组件失败:', error)
        this.$message.error('初始化失败，请刷新页面重试')
      }
    },

    // 加载环境列表
    async loadEnvironments() {
      // 验证 projectID
      if (!this.projectID || this.projectID <= 0) {
        console.warn('无效的 projectID，跳过环境加载:', this.projectID)
        return
      }

      try {
        console.log('加载环境列表，projectID:', this.projectID)
        const response = await getApiTestEnvironmentsApi(this.projectID)

        if (response.data.code) {
          this.environments = response.data.result || []
          console.log('环境列表加载成功:', this.environments.length, '个环境')
        } else {
          console.warn('环境列表加载失败:', response.data.message)
        }
      } catch (error) {
        console.error('加载环境失败:', error)
        // 不显示错误消息，因为这可能在初始化时发生
      }
    },

    async getRequestData(requestID) {
      try {
        const response = await getApiTestCaseDetailApi(this.projectID, parseInt(requestID))
        if (response.data.code) {
          return response.data.result
        }
        return null
      } catch (error) {
        console.error('获取请求数据失败:', error)
        return null
      }
    },

    // 切换请求标签页
    switchRequest(requestId) {
      try {
        const targetId = String(requestId);

        // 防止切换到 add-new 标签页
        if (targetId === 'add-new') {
          console.warn('尝试切换到新建标签页，已阻止');
          return;
        }

        this.activeRequestId = targetId;
        const request = this.requestTabs.find(r => r.id === targetId);

        if (request) {
          this.currentRequestData = { ...request };
          this.$emit('update-request', this.currentRequestData);
        } else {
          console.warn(`未找到ID为 ${targetId} 的请求标签页`);
        }
      } catch (error) {
        console.error('切换请求标签页失败:', error);
        this.$message.error('切换标签页失败');
      }
    },

    // 创建新请求
    createNewRequest() {
      // 防抖：如果正在创建请求，则忽略
      if (this.isCreatingNewRequest) {
        console.log('正在创建请求中，忽略重复操作');
        return;
      }

      try {
        this.isCreatingNewRequest = true;
        this.tabCounter++
        const newId = 'request_' + this.tabCounter

        // 检查是否已存在相同ID的标签页
        const existingTab = this.requestTabs.find(r => r.id === newId)
        if (existingTab) {
          this.switchRequest(newId);
          return;
        }

        const newRequest = {
          id: newId,
          collection: null,
          method: 'GET',
          name: `New Request ${this.tabCounter}`,
          url: '',
          description: '',
          headers: {},
          query_params: {},
          path_variables: {},
          body_type: 'none',
          body_data: {},
          auth_type: 'none',
          auth_config: {},
          pre_request_script: '',
          post_request_script: '',
          test_assertions: [],
          isNew: true
        };

        this.requestTabs.push(newRequest);

        // 使用 nextTick 确保DOM更新后再切换标签页
        this.$nextTick(() => {
          this.switchRequest(newId);
          // 重置创建状态
          setTimeout(() => {
            this.isCreatingNewRequest = false;
          }, 300);
        });
      } catch (error) {
        console.error('创建新请求失败:', error);
        this.$message.error('创建新请求失败');
        this.isCreatingNewRequest = false;
      }
    },

    // 从集合中打开请求
    async openRequestFromCollection(requestData) {
      // 如果传入的是 ID，需要先获取完整数据
      let fullRequestData = requestData
      if (typeof requestData === 'number' || (typeof requestData === 'object' && !requestData.method)) {
        fullRequestData = await this.getRequestData(requestData.id || requestData)
        if (!fullRequestData) {
          this.$message.error('获取请求数据失败')
          return
        }
      }

      const existingTab = this.requestTabs.find(r => r.id === String(fullRequestData.id))
      if (existingTab) {
        this.switchRequest(String(fullRequestData.id))
        return
      }

      this.requestTabs.push({
        ...fullRequestData,
        id: String(fullRequestData.id),
        isNew: false
      })
      this.switchRequest(String(fullRequestData.id))
    },

    // 关闭请求标签页
    closeRequestTab(requestId) {
      const index = this.requestTabs.findIndex(r => r.id === String(requestId));
      if (index > -1) {
        this.requestTabs.splice(index, 1);
        if (this.activeRequestId === String(requestId) && this.requestTabs.length > 0) {
          this.switchRequest(this.requestTabs[this.requestTabs.length - 1].id);
        } else if (this.requestTabs.length === 0) {
          this.activeRequestId = null
          this.currentRequestData = this.getEmptyRequestData()
        }
      }
    },

    // 获取空请求数据
    getEmptyRequestData() {
      return {
        id: null,
        collection: null,
        method: 'GET',
        url: '',
        name: 'New Request',
        description: '',
        headers: {},
        query_params: {},
        path_variables: {},
        body_type: 'none',
        body_data: {},
        auth_type: 'none',
        auth_config: {},
        pre_request_script: '',
        post_request_script: '',
        test_assertions: []
      }
    },

    // 切换内容标签页
    switchTab(tabKey) {
      this.activeTab = tabKey;
    },
    // 更新请求数据
    updateRequest() {
      const request = this.requestTabs.find(r => r.id === this.activeRequestId);
      if (request) {
        Object.assign(request, this.currentRequestData);
      }
      this.$emit('update-request', this.currentRequestData);
    },

    // URL 格式验证方法
    isValidUrl(url) {
      if (!url || typeof url !== 'string') {
        return false;
      }

      // 去除首尾空格
      url = url.trim();

      // 检查是否为空
      if (url === '') {
        return false;
      }

      try {
        // 如果没有协议，自动添加 http://
        if (!/^https?:\/\//i.test(url)) {
          url = 'http://' + url;
        }

        // 使用 URL 构造函数验证
        const urlObj = new URL(url);

        // 检查协议是否为 http 或 https
        if (!['http:', 'https:'].includes(urlObj.protocol)) {
          return false;
        }

        // 检查主机名是否有效
        if (!urlObj.hostname || urlObj.hostname === '') {
          return false;
        }

        return true;
      } catch (error) {
        return false;
      }
    },

    // 标准化 URL 格式
    normalizeUrl(url) {
      if (!url || typeof url !== 'string') {
        return url;
      }

      url = url.trim();

      // 如果没有协议，自动添加 http://
      if (url && !/^https?:\/\//i.test(url)) {
        return 'http://' + url;
      }

      return url;
    },

    // 发送请求
    async sendRequest() {
      if (!this.currentRequestData.url) {
        this.$message.warning('请输入请求URL')
        return
      }

      // 验证 URL 格式
      if (!this.isValidUrl(this.currentRequestData.url)) {
        this.$message.error('请输入有效的URL格式，例如：https://example.com/api 或 example.com/api')
        return
      }

      // 标准化 URL 格式
      this.currentRequestData.url = this.normalizeUrl(this.currentRequestData.url);

      // 同步更新标签页数据
      this.updateRequest();

      this.isLoading = true;

      try {
        const executeData = {
          environment_id: this.selectedEnvironmentId,
          override_config: {
            headers: this.currentRequestData.headers,
            query_params: this.currentRequestData.query_params,
            body_data: this.currentRequestData.body_data
          }
        }

        let response
        if (this.currentRequestData.id && !this.currentRequestData.isNew) {
          // 执行已保存的测试用例
          response = await executeApiTestCaseApi(this.projectID, this.currentRequestData.id, executeData)
        } else {
          // 临时执行未保存的请求
          const tempCase = await this.createTempTestCase()
          response = await executeApiTestCaseApi(this.projectID, tempCase.id, executeData)
        }

        if (response.data.code) {
          this.responseData = response.data.result.response
        } else {
          this.$message.error(response.data.message || '请求执行失败')
        }
      } catch (error) {
        this.$message.error(handleApiError(error))
      } finally {
        this.isLoading = false;
      }
    },

    // 创建临时测试用例用于执行
    async createTempTestCase() {
      const tempCaseData = {
        collection: this.currentRequestData.collection,
        name: this.currentRequestData.name || 'Temp Request',
        method: this.currentRequestData.method,
        url: this.currentRequestData.url,
        headers: this.currentRequestData.headers,
        query_params: this.currentRequestData.query_params,
        body_type: this.currentRequestData.body_type,
        body_data: this.currentRequestData.body_data,
        auth_type: this.currentRequestData.auth_type,
        auth_config: this.currentRequestData.auth_config,
        pre_request_script: this.currentRequestData.pre_request_script,
        post_request_script: this.currentRequestData.post_request_script,
        test_assertions: this.currentRequestData.test_assertions
      }

      const response = await createApiTestCaseApi(this.projectID, tempCaseData)
      if (response.data.code) {
        return response.data.result
      }
      throw new Error(response.data.message || '创建临时用例失败')
    },

    // 保存请求
    async saveRequest() {
      if (!this.currentRequestData.name || !this.currentRequestData.url) {
        this.$message.warning('请输入请求名称和URL')
        return
      }

      this.isSaving = true
      try {
        let response
        if (this.currentRequestData.id && !this.currentRequestData.isNew) {
          // 更新现有测试用例
          response = await updateApiTestCaseApi(this.projectID, this.currentRequestData.id, this.currentRequestData)
        } else {
          // 创建新测试用例
          response = await createApiTestCaseApi(this.projectID, this.currentRequestData)
        }

        if (response.data.code) {
          this.$message.success('保存成功')
          // 更新当前请求数据
          this.currentRequestData = { ...response.data.result, isNew: false }
          // 更新标签页数据
          const request = this.requestTabs.find(r => r.id === this.activeRequestId)
          if (request) {
            Object.assign(request, this.currentRequestData)
          }
          this.$emit('request-saved', response.data.result)
        } else {
          this.$message.error(response.data.message || '保存失败')
        }
      } catch (error) {
        this.$message.error(handleApiError(error))
      } finally {
        this.isSaving = false
      }
    },

    // 获取HTTP方法标签类型
    getMethodTagType(method) {
      const types = {
        'GET': 'success',
        'POST': 'primary',
        'PUT': 'warning',
        'DELETE': 'danger',
        'PATCH': 'info',
        'HEAD': 'info',
        'OPTIONS': 'info'
      };
      return types[method] || 'success';
    },

    // 处理标签页点击
    handleTabClick(tab) {
      // 如果点击的是新建标签页，触发创建新请求
      if (tab.name === 'add-new') {
        // 阻止默认的标签页切换行为
        this.$nextTick(() => {
          this.createNewRequest();
        });
        return;
      }
      this.switchRequest(tab.name);
    },

    // 处理环境命令
    handleEnvironmentCommand(command) {
      if (command === 'manage') {
        this.$emit('show-environment-modal');
      } else if (command === 'select') {
        this.showEnvironmentSelector()
      }
    },

    // 显示环境选择器
    showEnvironmentSelector() {
      if (this.environments.length === 0) {
        this.$message.info('暂无可用环境，请先创建环境')
        return
      }

      const options = this.environments.map(env => ({
        label: env.name,
        value: env.id
      }))

      this.$prompt('请选择环境', '选择环境', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputType: 'select',
        inputOptions: options
      }).then(({ value }) => {
        const selectedEnv = this.environments.find(env => env.id === value)
        if (selectedEnv) {
          this.selectedEnvironment = selectedEnv.name
          this.selectedEnvironmentId = selectedEnv.id
        }
      }).catch(() => {
        // 用户取消选择
      })
    },

    // 处理发送命令
    handleSendCommand(command) {
      if (command === 'send') {
        this.sendRequest();
      } else if (command === 'send-and-save') {
        this.sendAndSave();
      }
    },

    // 发送并保存
    async sendAndSave() {
      await this.saveRequest()
      if (!this.isSaving) {
        this.sendRequest()
      }
    },

    // 处理保存命令
    handleSaveCommand(command) {
      if (command === 'save') {
        this.saveRequest();
      } else if (command === 'save-as') {
        this.saveAsNewRequest();
      }
    },

    // 另存为新请求 - 显示对话框
    async saveAsNewRequest() {
      this.saveAsForm.name = this.currentRequestData.name + ' Copy'
      this.saveAsDialogVisible = true
      // 打开对话框时加载集合列表
      await this.loadCollections()
    },

    // 加载集合列表
    async loadCollections() {
      this.collectionsLoading = true
      try {
        const response = await getApiTestCollectionsApi(this.projectID)
        if (response.data.code) {
          this.collections = response.data.result || []
          // 如果有集合且没有选中的集合，默认选择第一个
          if (this.collections.length > 0 && !this.saveAsForm.selectedCollection) {
            this.saveAsForm.selectedCollection = this.collections[0].id
          }
        } else {
          this.$message.error(response.data.message || '加载集合列表失败')
        }
      } catch (error) {
        console.error('加载集合列表失败:', error)
        this.$message.error(handleApiError(error))
      } finally {
        this.collectionsLoading = false
      }
    },

    // 创建新集合
    async createNewCollection() {
      if (!this.newCollectionName.trim()) {
        this.$message.warning('请输入集合名称')
        return
      }

      this.creatingCollection = true
      try {
        const collectionData = {
          name: this.newCollectionName.trim(),
          description: '通过另存为创建的集合'
        }

        const response = await createApiTestCollectionApi(this.projectID, collectionData)
        if (response.data.code) {
          const newCollection = response.data.result
          this.collections.unshift(newCollection) // 添加到列表开头
          this.saveAsForm.selectedCollection = newCollection.id
          this.newCollectionName = ''
          this.showNewCollectionInput = false
          this.$message.success('集合创建成功')
        } else {
          this.$message.error(response.data.message || '创建集合失败')
        }
      } catch (error) {
        console.error('创建集合失败:', error)
        this.$message.error(handleApiError(error))
      } finally {
        this.creatingCollection = false
      }
    },

    // 确认另存为
    async confirmSaveAs() {
      if (!this.saveAsForm.name.trim()) {
        this.$message.warning('请输入请求名称')
        return
      }

      if (!this.saveAsForm.selectedCollection) {
        this.$message.warning('请选择保存位置')
        return
      }

      this.isSaving = true
      try {
        const newRequestData = {
          ...this.currentRequestData,
          name: this.saveAsForm.name.trim(),
          collection: this.saveAsForm.selectedCollection,
          id: null,
          isNew: true
        }

        const response = await createApiTestCaseApi(this.projectID, newRequestData)
        if (response.data.code) {
          this.$message.success('另存为成功')
          this.openRequestFromCollection(response.data.result)
          this.saveAsDialogVisible = false
          // 重置表单
          this.resetSaveAsForm()
        } else {
          this.$message.error(response.data.message || '另存为失败')
        }
      } catch (error) {
        this.$message.error(handleApiError(error))
      } finally {
        this.isSaving = false
      }
    },

    // 选择集合 - 添加缺失的方法
    selectCollection(collectionId) {
      this.saveAsForm.selectedCollection = collectionId
    },

    // 重置另存为表单
    resetSaveAsForm() {
      this.saveAsForm = {
        name: 'test',
        selectedCollection: null
      }
      this.showNewCollectionInput = false
      this.newCollectionName = ''
    }

    // ...existing code...
  },
  watch: {
    // 监听 projectID 变化
    projectID: {
      handler(newProjectID, oldProjectID) {
        console.log('projectID 变化:', oldProjectID, '->', newProjectID)
        if (newProjectID && newProjectID > 0 && newProjectID !== oldProjectID) {
          this.initializeComponent()
        }
      },
      immediate: false
    },

    async selectApiCase(selectNew) {
      if (selectNew && selectNew.method) {
        try {
          const requestInfo = await this.getRequestData(selectNew.id)
          if (requestInfo) {
            await this.openRequestFromCollection(requestInfo)
          }
        } catch (error) {
          console.error('打开请求失败:', error)
          this.$message.error('打开请求失败')
        }
      }
    },

    // 监听对话框关闭，重置表单
    saveAsDialogVisible(visible) {
      if (!visible) {
        this.resetSaveAsForm()
      }
    }
  },
  components: {
    ParametersTab,
    BodyTab,
    HeadersTab,
    AuthorizationTab,
    PreScriptTab,
    PostScriptTab,
    AssertionTab,
    ResponseArea
  }
}
</script>

<style scoped>
.api-case-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: white;
  overflow: hidden;
  position: relative;
  padding: 4px 8px 0 4px;
  /* border-bottom: 1px solid #e0e0e0;
  box-sizing: border-box;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04); */
}

/* 标签页基础样式优化 */
.api-case-content>>>.el-tabs__header {
  margin: 0;
  background: transparent;
}

.api-case-content>>>.el-tabs__nav-wrap {
  /* margin-bottom: 0; */
  position: relative;
}

.api-case-content>>>.el-tabs__nav {
  display: flex;
  align-items: center;
}

.api-case-content>>>.el-tabs__item {
  height: 36px;
  line-height: 36px;
  padding: 0 16px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid transparent;
  border-radius: 6px 6px 0 0;
  margin-right: 4px;
  transition: all 0.2s ease;
  color: #606266;
  background: #f8f9fa;
}

.api-case-content>>>.el-tabs__item:hover {
  background: rgba(24, 144, 255, 0.08);
  color: #1890ff;
  border-color: rgba(24, 144, 255, 0.2);
}

.api-case-content>>>.el-tabs__item.is-active {
  background: white;
  color: #1890ff;
  border-color: #e0e0e0;
  border-bottom-color: white;
  font-weight: 600;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.06);
}

.api-case-content>>>.el-tabs__content {
  padding: 0;
  height: calc(100% - 34px);
  overflow: hidden;
  /* background: #f5f5f5; */
}

.api-case-content>>>.el-tabs__active-bar {
  display: none;
}

/* 方法标签优化 */
.method-tag {
  font-size: 8px;
  padding: 2px 6px;
  margin-right: 8px;
  height: 18px;
  line-height: 14px;
  font-weight: 700;
  border-radius: 3px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 标签页内容区域优化 */
.tab-pane-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: white;
  overflow: hidden;
  /* border-radius: 0 8px 8px 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08); */
}

/* 请求构建区域优化 */
.request-builder {
  flex-shrink: 0;
  padding: 16px 20px;
  /* background: linear-gradient(135deg, #fafbfc 0%, #f5f7fa 100%); */
  /* border-bottom: 1px solid #e8ecf0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04); */
}

.request-header {
  display: flex;
  gap: 12px;
  align-items: center;
}

.method-select {
  width: 110px;
  flex-shrink: 0;
}

.method-select>>>.el-input__inner {
  font-weight: 600;
  text-align: center;
  border-radius: 6px;
  border: 2px solid #e0e6ed;
  transition: all 0.2s ease;
}

.method-select>>>.el-input__inner:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.1);
}

.url-input {
  flex: 1;
  min-width: 200px;
}

.url-input>>>.el-input__inner {
  font-family: 'Monaco', 'Menlo', 'Courier New', monospace;
  border-radius: 6px;
  border: 2px solid #e0e6ed;
  transition: all 0.2s ease;
  background: white;
}

.url-input>>>.el-input__inner:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.1);
}

.action-buttons-right {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

/* 请求内容区域优化 */
.request-content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0;
}

.content-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.content-tabs>>>.el-tabs__header {
  flex-shrink: 0;
  margin: 0;
  background: #f8f9fa;
  /* border-bottom: 1px solid #e8ecf0;
  padding: 0 8px; */
}

.content-tabs>>>.el-tabs__nav-wrap {
  margin-bottom: 0;
}

.content-tabs>>>.el-tabs__item {
  height: 40px;
  line-height: 40px;
  padding: 0 24px;
  font-size: 13px;
  font-weight: 500;
  color: #606266;
  border-radius: 6px 6px 0 0;
  margin-right: 2px;
  transition: all 0.2s ease;
}

.content-tabs>>>.el-tabs__item:hover {
  color: #1890ff;
  background: rgba(24, 144, 255, 0.04);
}

.content-tabs>>>.el-tabs__item.is-active {
  color: #1890ff;
  background: white;
  font-weight: 600;
  border: 1px solid #e8ecf0;
  border-bottom: 1px solid white;
  /* margin-bottom: -1px; */
}

.content-tabs>>>.el-tabs__content {
  flex: 1;
  padding: 20px;
  overflow: auto;
  min-height: 0;
  background: white;
}

.content-tabs>>>.el-tabs__active-bar {
  display: none;
}

/* 环境选择器优化 */
.environment-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0 12px;
}

.env-selector {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  border: 2px solid #e0e6ed;
  border-radius: 6px;
  cursor: pointer;
  background: white;
  font-size: 12px;
  height: 32px;
  transition: all 0.2s ease;
  min-width: 130px;
  font-weight: 500;
}

.env-selector:hover {
  border-color: #1890ff;
  box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.1);
  transform: translateY(-1px);
}

.env-view-btn {
  width: 28px !important;
  height: 28px !important;
  min-width: 28px !important;
  padding: 0 !important;
  border-radius: 6px !important;
  border: 2px solid #e0e6ed !important;
  transition: all 0.2s ease !important;
}

.env-view-btn:hover {
  border-color: #1890ff !important;
  color: #1890ff !important;
  transform: translateY(-1px) !important;
}

/* 新建标签页优化 */
.add-new-tab {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #909399;
  cursor: pointer;
  padding: 0 12px;
  transition: all 0.2s ease;
  height: 100%;
  border-radius: 6px;
  border: 2px dashed #d0d7de;
  background: #f8f9fa;
}

.add-new-tab:hover {
  color: #1890ff;
  border-color: #1890ff;
  background: rgba(24, 144, 255, 0.04);
  transform: translateY(-1px);
}

.add-new-tab i {
  font-size: 14px;
  font-weight: bold;
}

/* 标签页容器 */
.tabs-container {
  position: relative;
}

/* 环境选择器悬浮样式 */
.environment-selector-float {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 10;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 2px 8px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.environment-selector-float .env-selector {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 8px;
  border: 2px solid #e0e6ed;
  border-radius: 6px;
  cursor: pointer;
  background: white;
  font-size: 12px;
  height: 32px;
  transition: all 0.2s ease;
  min-width: 130px;
  font-weight: 500;
}

.environment-selector-float .env-selector:hover {
  border-color: #1890ff;
  box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.1);
  transform: translateY(-1px);
}

.environment-selector-float .env-view-btn {
  width: 28px !important;
  height: 28px !important;
  min-width: 28px !important;
  padding: 0 !important;
  border-radius: 6px !important;
  border: 2px solid #e0e6ed !important;
  transition: all 0.2s ease !important;
}

.environment-selector-float .env-view-btn:hover {
  border-color: #1890ff !important;
  color: #1890ff !important;
  transform: translateY(-1px) !important;
}

/* 调整标签页导航区域，为悬浮组件留出空间 */
.api-case-content>>>.el-tabs__nav-wrap {
  padding-right: 260px;
}

/* 新建标签页样式调整 */
.api-case-content>>>.el-tabs__item[aria-controls*="add-new"] {
  width: 48px;
  min-width: 48px;
  padding: 4px;
  text-align: center;
  background: transparent;
  border: none;
}

.api-case-content>>>.el-tabs__item[aria-controls*="add-new"]:hover {
  background: transparent;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .request-header {
    flex-wrap: wrap;
    gap: 8px;
  }

  .method-select {
    width: 100px;
  }

  .url-input {
    min-width: 150px;
  }

  .api-case-content>>>.el-tabs__nav-wrap {
    padding-right: 220px;
  }

  .environment-selector-float .env-selector {
    min-width: 110px;
    font-size: 11px;
  }
}

@media (max-width: 768px) {
  .api-case-content {
    padding: 4px 8px 0 8px;
    max-height: 80px;
  }

  .request-builder {
    padding: 12px 16px;
  }

  .request-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .method-select,
  .url-input {
    width: 100%;
  }

  .action-buttons-right {
    width: 100%;
    justify-content: space-between;
  }

  .api-case-content>>>.el-tabs__content {
    height: calc(100% - 80px);
  }

  .api-case-content>>>.el-tabs__nav-wrap {
    padding-right: 0;
  }

  .environment-selector-float {
    position: static;
    margin-top: 8px;
    justify-content: center;
    background: transparent;
    box-shadow: none;
    backdrop-filter: none;
  }

  .environment-selector-float .env-selector {
    min-width: auto;
    flex: 1;
  }

  /* 移动端按钮优化 */
  .api-case-content>>>.el-button--mini {
    padding: 6px 12px;
    height: 28px;
    font-size: 11px;
  }

  .api-case-content>>>.el-input--mini .el-input__inner {
    height: 28px;
    line-height: 28px;
    font-size: 11px;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载状态优化 */
/* .api-case-content>>>.el-button.is-loading {
  pointer-events: none;
  opacity: 0.8;
} */

/* 滚动条优化 */
/* .content-tabs>>>.el-tabs__content::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.content-tabs>>>.el-tabs__content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.content-tabs>>>.el-tabs__content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
  transition: background 0.2s ease;
}

.content-tabs>>>.el-tabs__content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
} */

/* 焦点状态优化 */
/* .api-case-content>>>.el-input__inner:focus,
.api-case-content>>>.el-textarea__inner:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.1);
} */

/* 另存为对话框样式 */
.save-as-dialog>>>.el-dialog {
  border-radius: 8px;
}

.save-as-dialog>>>.el-dialog__header {
  padding: 20px 20px 10px;
  border-bottom: 1px solid #f0f0f0;
}

.save-as-dialog>>>.el-dialog__title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.save-as-dialog>>>.el-dialog__body {
  padding: 20px;
}

.save-as-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  font-size: 13px;
  font-weight: 500;
  color: #262626;
}

.form-input>>>.el-input__inner {
  border-radius: 6px;
  border: 1px solid #d9d9d9;
  font-size: 13px;
  height: 36px;
}

.form-input>>>.el-input__suffix {
  right: 8px;
}

.form-input>>>.el-button {
  padding: 0;
  width: 24px;
  height: 24px;
  border: none;
  color: #8c8c8c;
}


.collection-list {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
}

.new-collection-item,
.collection-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
  font-size: 13px;
}

.new-collection-item:hover,
.collection-item:hover {
  background-color: #f5f5f5;
}

.collection-item.active,
.collection-item.selected {
  background-color: #e6f7ff;
  border-color: #91d5ff;
}

.collection-item:last-child {
  border-bottom: none;
}

.new-collection-item i:first-child,
.collection-item i:first-child {
  color: #1890ff;
  font-size: 14px;
}

.collection-check {
  color: #52c41a !important;
  font-size: 16px !important;
  margin-right: -4px;
}

.help-icon {
  margin-left: auto;
  color: #bfbfbf;
  font-size: 12px;
}

.more-btn {
  margin-left: auto;
  color: #8c8c8c;
  padding: 4px;
}

.more-btn:hover {
  color: #1890ff;
}

.new-collection-input {
  padding: 8px 16px;
  background-color: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.new-collection-input>>>.el-input__inner {
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  height: 32px;
  font-size: 12px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 20px 20px;
  border-top: 1px solid #f0f0f0;
}

.dialog-footer .el-button {
  padding: 8px 16px;
  font-size: 13px;
  border-radius: 6px;
  font-weight: 500;
}

.dialog-footer .el-button--primary {
  background: #1890ff;
  border-color: #1890ff;
}

.dialog-footer .el-button--primary:hover {
  background: #40a9ff;
  border-color: #40a9ff;
}

/* 新增样式 */
.new-collection-input .el-input-group__append {
  padding: 0 8px;
}

.new-collection-input .el-button {
  margin: 0;
  border-radius: 0;
}

.empty-collections {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 16px;
  color: #909399;
  font-size: 13px;
  text-align: center;
}

.empty-collections i {
  font-size: 32px;
  color: #c0c4cc;
  margin-bottom: 8px;
}

.collection-list.el-loading-parent--relative {
  min-height: 100px;
}

/* 加载状态样式调整 */
.collection-list .el-loading-mask {
  border-radius: 6px;
}
</style>